import React, { useState, useEffect } from 'react';
import { SearchIcon, EyeIcon, CheckIcon, XIcon, ClockIcon, ArrowUpDownIcon, FilterIcon, ChevronLeftIcon, ChevronRightIcon, PhoneIcon, MailIcon, MessageSquareIcon, CalendarIcon, FileTextIcon, MoreHorizontalIcon, ArrowLeftIcon, MenuIcon } from 'lucide-react';
import AdminSidebar from '../components/AdminSidebar';
import { apiClient } from '../services/api';

const CarInquiries = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortField, setSortField] = useState('inquiryDate');
  const [sortDirection, setSortDirection] = useState('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedInquiry, setSelectedInquiry] = useState(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const itemsPerPage = 10;

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  // State for car inquiries
  const [carInquiries, setCarInquiries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch car inquiries from API
  useEffect(() => {
    const fetchCarInquiries = async () => {
      try {
        setLoading(true);

        // Use apiClient which automatically handles authentication headers
        const response = await apiClient.get('/car-inquiries');
        setCarInquiries(response.data?.data || []);
      } catch (err) {
        console.error('Error fetching car inquiries:', err);
        if (err.status === 401) {
          setError('Authentication required');
        } else {
          setError('Failed to load car inquiries');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchCarInquiries();
  }, []);

  // Filter inquiries based on search query and status
  const filteredInquiries = carInquiries.filter(inquiry => {
    if (!inquiry) return false;
    const searchLower = searchQuery.toLowerCase();
    const matchesSearch = 
      inquiry.customer_name?.toLowerCase().includes(searchLower) ||
      inquiry.id?.toString().toLowerCase().includes(searchLower) ||
      `${inquiry.vehicle?.year} ${inquiry.vehicle?.make} ${inquiry.vehicle?.model}`.toLowerCase().includes(searchLower);
    const matchesStatus = statusFilter === 'all' || inquiry.status?.toLowerCase() === statusFilter.toLowerCase();
    return matchesSearch && matchesStatus;
  });

  // Sort inquiries
  const sortedInquiries = [...filteredInquiries].sort((a, b) => {
    if (sortField === 'inquiryDate') {
      return sortDirection === 'asc' 
        ? new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        : new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
    }
    if (sortField === 'customer') {
      return sortDirection === 'asc' 
        ? a.customer_name.localeCompare(b.customer_name) 
        : b.customer_name.localeCompare(a.customer_name);
    }
    return 0;
  });

  // Pagination
  const totalPages = Math.ceil(sortedInquiries.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedInquiries = sortedInquiries.slice(startIndex, startIndex + itemsPerPage);

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      'pending': { bg: 'bg-yellow-100', text: 'text-yellow-800', icon: ClockIcon },
      'contacted': { bg: 'bg-blue-100', text: 'text-blue-800', icon: PhoneIcon },
      'scheduled': { bg: 'bg-purple-100', text: 'text-purple-800', icon: CalendarIcon },
      'completed': { bg: 'bg-green-100', text: 'text-green-800', icon: CheckIcon },
      'not interested': { bg: 'bg-gray-100', text: 'text-gray-800', icon: XIcon },
      'sold': { bg: 'bg-emerald-100', text: 'text-emerald-800', icon: CheckIcon }
    };

    const config = statusConfig[status?.toLowerCase()] || statusConfig['pending'];
    const IconComponent = config.icon;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        <IconComponent className="h-3 w-3 mr-1" />
        {status}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="flex h-screen bg-gray-100">
        <aside className="bg-[#1e3a5f] text-white w-64 flex-shrink-0 hidden md:block">
          <AdminSidebar activePage="car-inquiries" />
        </aside>
        <div className="flex-1 flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-screen bg-gray-100">
        <aside className="bg-[#1e3a5f] text-white w-64 flex-shrink-0 hidden md:block">
          <AdminSidebar activePage="car-inquiries" />
        </aside>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Error Loading Car Inquiries</h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <button 
              onClick={() => window.location.reload()} 
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (selectedInquiry) {
    return (
      <div className="flex h-screen bg-gray-100">
        <aside className="bg-[#1e3a5f] text-white w-64 flex-shrink-0 hidden md:block">
          <AdminSidebar activePage="car-inquiries" />
        </aside>
        <main className="flex-1 overflow-auto">
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <button
                  onClick={() => setSelectedInquiry(null)}
                  className="mr-4 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded-lg transition-colors"
                >
                  <ArrowLeftIcon className="h-5 w-5" />
                </button>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Car Inquiry Details</h1>
                  <p className="text-gray-600">Inquiry ID: {selectedInquiry.id}</p>
                </div>
              </div>
              {getStatusBadge(selectedInquiry.status)}
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Customer Information */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer Information</h3>
                  <div className="space-y-3">
                    <div>
                      <p className="text-gray-500 text-sm">Name</p>
                      <p className="font-medium">{selectedInquiry.customer_name}</p>
                    </div>
                    <div className="flex items-center">
                      <PhoneIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <a href={`tel:${selectedInquiry.phone}`} className="text-blue-600 hover:text-blue-800">
                        {selectedInquiry.phone}
                      </a>
                    </div>
                    <div className="flex items-center">
                      <MailIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <a href={`mailto:${selectedInquiry.email}`} className="text-blue-600 hover:text-blue-800">
                        {selectedInquiry.email}
                      </a>
                    </div>
                  </div>
                </div>

                {/* Vehicle Information */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Vehicle Information</h3>
                  <div className="space-y-3">
                    <div>
                      <p className="text-gray-500 text-sm">Vehicle</p>
                      <p className="font-medium">
                        {selectedInquiry.vehicle?.year} {selectedInquiry.vehicle?.make} {selectedInquiry.vehicle?.model}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-500 text-sm">Price</p>
                      <p className="font-semibold text-lg text-blue-700">
                        ${parseFloat(selectedInquiry.vehicle?.price || 0).toLocaleString()}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Inquiry Details */}
              <div className="mt-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Inquiry Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <p className="text-gray-500 text-sm">Inquiry Date</p>
                    <div className="flex items-center">
                      <CalendarIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <p>{new Date(selectedInquiry.created_at).toLocaleDateString()}</p>
                    </div>
                  </div>
                  <div>
                    <p className="text-gray-500 text-sm">Inquiry Type</p>
                    <p className="font-medium capitalize">{selectedInquiry.inquiry_type}</p>
                  </div>
                </div>
                
                <div className="mt-6">
                  <p className="text-gray-500 text-sm mb-2">Message</p>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <p className="text-gray-900">{selectedInquiry.message}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-100">
      <aside className="bg-[#1e3a5f] text-white w-64 flex-shrink-0 hidden md:block">
        <AdminSidebar activePage="car-inquiries" />
      </aside>
      <main className="flex-1 overflow-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Car Inquiries</h1>
              <p className="text-gray-600">Manage customer vehicle inquiries</p>
            </div>
            <button
              onClick={toggleSidebar}
              className="md:hidden p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-200 rounded-lg transition-colors"
            >
              <MenuIcon className="h-6 w-6" />
            </button>
          </div>

          {/* Filters and Search */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <input
                    type="text"
                    placeholder="Search inquiries..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Status</option>
                  <option value="pending">Pending</option>
                  <option value="contacted">Contacted</option>
                  <option value="scheduled">Scheduled</option>
                  <option value="completed">Completed</option>
                  <option value="not interested">Not Interested</option>
                  <option value="sold">Sold</option>
                </select>
              </div>
            </div>
          </div>

          {/* Inquiries Table */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <button
                        onClick={() => handleSort('customer')}
                        className="flex items-center hover:text-gray-700"
                      >
                        Customer
                        <ArrowUpDownIcon className="ml-1 h-4 w-4" />
                      </button>
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Vehicle
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Inquiry Type
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <button
                        onClick={() => handleSort('inquiryDate')}
                        className="flex items-center hover:text-gray-700"
                      >
                        Date
                        <ArrowUpDownIcon className="ml-1 h-4 w-4" />
                      </button>
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {paginatedInquiries.length > 0 ? (
                    paginatedInquiries.map((inquiry) => (
                      <tr key={inquiry.id} className="hover:bg-gray-50">
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {inquiry.customer_name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {inquiry.phone}
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          {inquiry.vehicle?.year} {inquiry.vehicle?.make} {inquiry.vehicle?.model}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900 capitalize">
                          {inquiry.inquiry_type}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          {getStatusBadge(inquiry.status)}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(inquiry.created_at).toLocaleDateString()}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                          <button
                            onClick={() => setSelectedInquiry(inquiry)}
                            className="text-blue-600 hover:text-blue-900 mr-3"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={6} className="px-4 py-8 text-center text-gray-500">
                        No car inquiries found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <p className="text-sm text-gray-700">
                      Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, sortedInquiries.length)} of {sortedInquiries.length} results
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ChevronLeftIcon className="h-4 w-4" />
                    </button>
                    <span className="text-sm text-gray-700">
                      Page {currentPage} of {totalPages}
                    </span>
                    <button
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ChevronRightIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
};

export default CarInquiries;
