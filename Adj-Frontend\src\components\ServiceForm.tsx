import React, { useState, <PERSON> } from 'react';
import { motion } from 'framer-motion';
import { CheckCircleIcon, User, Phone, Mail, Settings, Car, FileText, Calendar } from 'lucide-react';
import { apiClient } from '../services/api';
const ServiceForm = () => {
  const [formData, setFormData] = useState({
    customer_name: '',
    phone: '',
    email: '',
    service_type: '',
    vehicle_make: '',
    vehicle_model: '',
    vehicle_year: '',
    description: ''
  });
  const [formStatus, setFormStatus] = useState({
    submitted: false,
    submitting: false,
    error: null
  });
  const handleChange = e => {
    const {
      name,
      value
    } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  const handleSubmit = async (e) => {
    e.preventDefault();
    setFormStatus({
      submitted: false,
      submitting: true,
      error: null
    });

    try {
      await apiClient.post('/service-requests', formData);
      setFormStatus({
        submitted: true,
        submitting: false,
        error: null
      });
      // Reset form
      setFormData({
        customer_name: '',
        phone: '',
        email: '',
        service_type: '',
        vehicle_make: '',
        vehicle_model: '',
        vehicle_year: '',
        description: ''
      });
    } catch (err) {
      console.error('Error submitting service request:', err);
      setFormStatus({
        submitted: false,
        submitting: false,
        error: 'Failed to submit service request. Please try again.'
      });
    }
  };
  const serviceOptions = [
    'Transmission Rebuilding',
    'Engine Repair & Rebuilding',
    'Advanced Diagnostics',
    'Brake Service',
    'Electrical Service',
    'Heating & Air Conditioning',
    'Suspension & Steering',
    'Exhaust Service',
    'Key Programming',
    'General Repair',
    'Fleet Service',
    'Oil Change & Inspection',
    'A/C Repair',
    'Other'
  ];
  const formVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 20,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.4
      }
    }
  };
  const successVariants = {
    hidden: {
      scale: 0.8,
      opacity: 0
    },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 400,
        damping: 10
      }
    }
  };
  if (formStatus.submitted) {
    return (
      <motion.div 
        className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden max-w-3xl mx-auto text-center" 
        variants={successVariants} 
        initial="hidden" 
        animate="visible"
      >
        <div className="bg-gradient-to-r from-green-500 to-emerald-600 px-6 py-4">
          <CheckCircleIcon className="h-8 w-8 text-white mx-auto" />
        </div>
        <div className="p-8">
          <div className="mx-auto w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mb-6">
            <CheckCircleIcon className="h-12 w-12 text-green-600" />
          </div>
          <h3 className="text-2xl font-bold text-gray-900 mb-3">
            Request Submitted Successfully!
          </h3>
          <p className="text-gray-600 mb-6 max-w-md mx-auto">
            Thank you for your service request. Our expert team will review your information and contact you within 24 hours with a detailed estimate.
          </p>
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <p className="text-green-700 text-sm font-medium">
              ✅ Your request has been received and is being processed
            </p>
          </div>
          <button 
            onClick={() => setFormStatus({
              submitted: false,
              submitting: false,
              error: null
            })} 
            className="bg-gradient-to-r from-[#1e3a5f] to-blue-700 hover:from-blue-800 hover:to-blue-900 text-white font-semibold py-3 px-8 rounded-lg transition-all duration-200 transform hover:scale-[1.02]"
          >
            Submit Another Request
          </button>
        </div>
      </motion.div>
    );
  }
  return (
    <motion.div 
      className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden max-w-3xl mx-auto"
      variants={formVariants} 
      initial="hidden" 
      animate="visible"
    >
      {/* Form Header */}
      <div className="bg-gradient-to-r from-[#1e3a5f] to-blue-700 px-6 py-4">
        <motion.div variants={itemVariants} className="flex items-center">
          <Calendar className="h-6 w-6 text-white mr-3" />
          <h2 className="text-xl font-bold text-white">
            Book Your Appointment
          </h2>
        </motion.div>
      </div>

      <form onSubmit={handleSubmit} className="p-6">
        <div className="space-y-6">
          {/* Customer Information */}
          <motion.div variants={itemVariants}>
            <div className="flex items-center mb-4">
              <User className="h-5 w-5 text-blue-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">
                Customer Information
              </h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="relative">
                <label htmlFor="customer_name" className="block text-sm font-medium text-gray-700 mb-1">
                  Full Name *
                </label>
                <input
                  type="text"
                  id="customer_name"
                  name="customer_name"
                  value={formData.customer_name}
                  onChange={handleChange}
                  required
                  placeholder="Enter your full name"
                  className="w-full px-3 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all text-sm"
                />
              </div>
              <div className="relative">
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                  Phone Number
                </label>
                <input 
                  type="tel" 
                  id="phone" 
                  name="phone" 
                  value={formData.phone} 
                  onChange={handleChange} 
                  placeholder="(*************"
                  className="w-full px-3 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all text-sm" 
                />
              </div>
            </div>
            <div className="mt-4">
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                Email Address *
              </label>
              <input 
                type="email" 
                id="email" 
                name="email" 
                value={formData.email} 
                onChange={handleChange} 
                required 
                placeholder="<EMAIL>"
                className="w-full px-3 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all text-sm" 
              />
            </div>
          </motion.div>

          {/* Service Information */}
          <motion.div variants={itemVariants}>
            <div className="flex items-center mb-4">
              <Settings className="h-5 w-5 text-blue-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">
                Service Information
              </h3>
            </div>
            <div>
              <label htmlFor="serviceType" className="block text-sm font-medium text-gray-700 mb-1">
                Service Type *
              </label>
              <select
                id="service_type"
                name="service_type"
                value={formData.service_type}
                onChange={handleChange}
                required
                className="w-full px-3 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all text-sm"
              >
                <option value="">Select the service you need...</option>
                {serviceOptions.map((service, index) => (
                  <option key={index} value={service}>
                    {service}
                  </option>
                ))}
              </select>
            </div>
          </motion.div>

          {/* Vehicle Information */}
          <motion.div variants={itemVariants}>
            <div className="flex items-center mb-4">
              <Car className="h-5 w-5 text-blue-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">
                Vehicle Information
              </h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label htmlFor="make" className="block text-sm font-medium text-gray-700 mb-1">
                  Make
                </label>
                <input
                  type="text"
                  id="vehicle_make"
                  name="vehicle_make"
                  value={formData.vehicle_make}
                  onChange={handleChange}
                  placeholder="e.g., Toyota, Ford"
                  className="w-full px-3 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all text-sm"
                />
              </div>
              <div>
                <label htmlFor="vehicle_model" className="block text-sm font-medium text-gray-700 mb-1">
                  Model
                </label>
                <input
                  type="text"
                  id="vehicle_model"
                  name="vehicle_model"
                  value={formData.vehicle_model}
                  onChange={handleChange}
                  placeholder="e.g., Camry, F-150"
                  className="w-full px-3 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all text-sm"
                />
              </div>
              <div>
                <label htmlFor="vehicle_year" className="block text-sm font-medium text-gray-700 mb-1">
                  Year
                </label>
                <input
                  type="text"
                  id="vehicle_year"
                  name="vehicle_year"
                  value={formData.vehicle_year}
                  onChange={handleChange}
                  placeholder="e.g., 2020"
                  required
                  className="w-full px-3 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all text-sm"
                />
              </div>
            </div>
          </motion.div>

          {/* Service Description */}
          <motion.div variants={itemVariants}>
            <div className="flex items-center mb-2">
              <FileText className="h-5 w-5 text-blue-600 mr-2" />
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                Service Description *
              </label>
            </div>
            <textarea 
              id="description" 
              name="description" 
              value={formData.description} 
              onChange={handleChange} 
              required 
              rows={3} 
              className="w-full px-3 py-2.5 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all text-sm resize-none" 
              placeholder="Please describe the issue or service needed in detail..."
            />
          </motion.div>

          {/* Error Message */}
          {formStatus.error && (
            <motion.div variants={itemVariants} className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-red-700 text-sm font-medium">
                ❌ {formStatus.error}
              </p>
            </motion.div>
          )}

          {/* Submit Button */}
          <motion.div variants={itemVariants} className="pt-2">
            <button
              type="submit"
              disabled={formStatus.submitting}
              className={`w-full bg-gradient-to-r from-[#1e3a5f] to-blue-700 hover:from-blue-800 hover:to-blue-900 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-[1.02] ${formStatus.submitting ? 'opacity-70 cursor-not-allowed hover:scale-100' : ''}`}
            >
              {formStatus.submitting ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Submitting Request...
                </div>
              ) : (
                'Submit Quote Request'
              )}
            </button>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mt-4">
              <p className="text-blue-700 text-sm text-center font-medium">
                🕒 Quick Response: We'll contact you within 24 hours with your estimate
              </p>
            </div>
          </motion.div>
        </div>
      </form>
    </motion.div>
  );
};
export default ServiceForm;