<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
</head>
<body>
    <h1>Service Requests API Test</h1>
    <button onclick="testLogin()">1. Test Login</button>
    <button onclick="testServiceRequests()">2. Test Service Requests</button>
    <div id="results"></div>

    <script>
        let authToken = null;

        async function testLogin() {
            try {
                const response = await fetch('http://127.0.0.1:8000/api/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                });

                const data = await response.json();
                console.log('Login response:', data);
                
                if (data.token) {
                    authToken = data.token;
                    document.getElementById('results').innerHTML = '<p>✅ Login successful! Token: ' + data.token.substring(0, 20) + '...</p>';
                } else {
                    document.getElementById('results').innerHTML = '<p>❌ Login failed: ' + JSON.stringify(data) + '</p>';
                }
            } catch (error) {
                console.error('Login error:', error);
                document.getElementById('results').innerHTML = '<p>❌ Login error: ' + error.message + '</p>';
            }
        }

        async function testServiceRequests() {
            if (!authToken) {
                document.getElementById('results').innerHTML = '<p>❌ Please login first</p>';
                return;
            }

            try {
                const response = await fetch('http://127.0.0.1:8000/api/service-requests', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': 'Bearer ' + authToken
                    }
                });

                const data = await response.json();
                console.log('Service requests response:', data);
                
                document.getElementById('results').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                console.error('Service requests error:', error);
                document.getElementById('results').innerHTML = '<p>❌ Service requests error: ' + error.message + '</p>';
            }
        }
    </script>
</body>
</html>
