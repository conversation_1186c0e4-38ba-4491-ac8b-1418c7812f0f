-- =====================================================
-- ADJ Automotive Repair Services - Complete Database
-- =====================================================
-- Database: adj_automotive
-- Version: 1.0
-- Created for: Laravel API Backend Integration
-- =====================================================

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- Create Database
CREATE DATABASE IF NOT EXISTS `adj_automotive` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `adj_automotive`;

-- =====================================================
-- 1. ADMIN USERS TABLE
-- =====================================================
CREATE TABLE `admin_users` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','manager','staff') NOT NULL DEFAULT 'staff',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `last_login_at` timestamp NULL DEFAULT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `admin_users_email_unique` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 2. CUSTOMERS TABLE
-- =====================================================
CREATE TABLE `customers` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `state` varchar(50) DEFAULT NULL,
  `zip_code` varchar(10) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `customers_email_unique` (`email`),
  KEY `customers_phone_index` (`phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 3. SERVICE TYPES TABLE
-- =====================================================
CREATE TABLE `service_types` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `base_price` decimal(10,2) DEFAULT NULL,
  `estimated_duration` int(11) DEFAULT NULL COMMENT 'Duration in minutes',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `display_order` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `service_types_name_unique` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 4. SERVICE REQUESTS TABLE
-- =====================================================
CREATE TABLE `service_requests` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `customer_id` bigint(20) UNSIGNED DEFAULT NULL,
  `customer_name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `service_type_id` bigint(20) UNSIGNED DEFAULT NULL,
  `service_type` varchar(255) NOT NULL,
  `vehicle_make` varchar(100) DEFAULT NULL,
  `vehicle_model` varchar(100) DEFAULT NULL,
  `vehicle_year` varchar(4) DEFAULT NULL,
  `vehicle_vin` varchar(17) DEFAULT NULL,
  `description` text NOT NULL,
  `status` enum('pending','confirmed','in_progress','completed','cancelled') NOT NULL DEFAULT 'pending',
  `priority` enum('low','normal','high','urgent') NOT NULL DEFAULT 'normal',
  `estimated_cost` decimal(10,2) DEFAULT NULL,
  `final_cost` decimal(10,2) DEFAULT NULL,
  `scheduled_date` datetime DEFAULT NULL,
  `completed_date` datetime DEFAULT NULL,
  `assigned_to` bigint(20) UNSIGNED DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `service_requests_customer_id_foreign` (`customer_id`),
  KEY `service_requests_service_type_id_foreign` (`service_type_id`),
  KEY `service_requests_assigned_to_foreign` (`assigned_to`),
  KEY `service_requests_status_index` (`status`),
  KEY `service_requests_scheduled_date_index` (`scheduled_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 5. VEHICLES TABLE
-- =====================================================
CREATE TABLE `vehicles` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `make` varchar(100) NOT NULL,
  `model` varchar(100) NOT NULL,
  `year` int(11) NOT NULL,
  `price` decimal(12,2) NOT NULL,
  `mileage` varchar(50) DEFAULT NULL,
  `engine` varchar(255) DEFAULT NULL,
  `transmission` varchar(100) DEFAULT NULL,
  `drivetrain` varchar(50) DEFAULT NULL,
  `fuel_type` varchar(50) DEFAULT NULL,
  `exterior_color` varchar(50) DEFAULT NULL,
  `interior_color` varchar(50) DEFAULT NULL,
  `vin` varchar(17) DEFAULT NULL,
  `stock_number` varchar(50) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `features` json DEFAULT NULL,
  `status` enum('available','pending_sale','sold','maintenance') NOT NULL DEFAULT 'available',
  `category` enum('sedan','suv','truck','sports','luxury','compact','convertible') DEFAULT NULL,
  `featured` tinyint(1) NOT NULL DEFAULT 0,
  `views` int(11) NOT NULL DEFAULT 0,
  `condition` enum('new','used','certified') NOT NULL DEFAULT 'used',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `vehicles_vin_unique` (`vin`),
  UNIQUE KEY `vehicles_stock_number_unique` (`stock_number`),
  KEY `vehicles_make_model_index` (`make`,`model`),
  KEY `vehicles_status_index` (`status`),
  KEY `vehicles_featured_index` (`featured`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 6. VEHICLE IMAGES TABLE
-- =====================================================
CREATE TABLE `vehicle_images` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `vehicle_id` bigint(20) UNSIGNED NOT NULL,
  `image_url` varchar(500) NOT NULL,
  `image_path` varchar(500) NOT NULL,
  `alt_text` varchar(255) DEFAULT NULL,
  `is_primary` tinyint(1) NOT NULL DEFAULT 0,
  `display_order` int(11) NOT NULL DEFAULT 0,
  `file_size` int(11) DEFAULT NULL,
  `mime_type` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `vehicle_images_vehicle_id_foreign` (`vehicle_id`),
  KEY `vehicle_images_is_primary_index` (`is_primary`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 7. CAR INQUIRIES TABLE
-- =====================================================
CREATE TABLE `car_inquiries` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `vehicle_id` bigint(20) UNSIGNED NOT NULL,
  `customer_id` bigint(20) UNSIGNED DEFAULT NULL,
  `customer_name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `message` text DEFAULT NULL,
  `inquiry_type` enum('general','test_drive','financing','trade_in') NOT NULL DEFAULT 'general',
  `status` enum('pending','contacted','scheduled','sold','cancelled') NOT NULL DEFAULT 'pending',
  `contacted_at` timestamp NULL DEFAULT NULL,
  `scheduled_date` datetime DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `car_inquiries_vehicle_id_foreign` (`vehicle_id`),
  KEY `car_inquiries_customer_id_foreign` (`customer_id`),
  KEY `car_inquiries_status_index` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 8. PERSONAL ACCESS TOKENS (Laravel Sanctum)
-- =====================================================
CREATE TABLE `personal_access_tokens` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `tokenable_type` varchar(255) NOT NULL,
  `tokenable_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `token` varchar(64) NOT NULL,
  `abilities` text DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 9. COMPANY SETTINGS TABLE
-- =====================================================
CREATE TABLE `company_settings` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `key` varchar(255) NOT NULL,
  `value` text DEFAULT NULL,
  `type` enum('string','number','boolean','json') NOT NULL DEFAULT 'string',
  `description` text DEFAULT NULL,
  `is_public` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `company_settings_key_unique` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- FOREIGN KEY CONSTRAINTS
-- =====================================================
ALTER TABLE `service_requests`
  ADD CONSTRAINT `service_requests_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `service_requests_service_type_id_foreign` FOREIGN KEY (`service_type_id`) REFERENCES `service_types` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `service_requests_assigned_to_foreign` FOREIGN KEY (`assigned_to`) REFERENCES `admin_users` (`id`) ON DELETE SET NULL;

ALTER TABLE `vehicle_images`
  ADD CONSTRAINT `vehicle_images_vehicle_id_foreign` FOREIGN KEY (`vehicle_id`) REFERENCES `vehicles` (`id`) ON DELETE CASCADE;

ALTER TABLE `car_inquiries`
  ADD CONSTRAINT `car_inquiries_vehicle_id_foreign` FOREIGN KEY (`vehicle_id`) REFERENCES `vehicles` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `car_inquiries_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE SET NULL;

-- =====================================================
-- AUTO INCREMENT VALUES
-- =====================================================
ALTER TABLE `admin_users` AUTO_INCREMENT=1;
ALTER TABLE `customers` AUTO_INCREMENT=1;
ALTER TABLE `service_types` AUTO_INCREMENT=1;
ALTER TABLE `service_requests` AUTO_INCREMENT=1;
ALTER TABLE `vehicles` AUTO_INCREMENT=1;
ALTER TABLE `vehicle_images` AUTO_INCREMENT=1;
ALTER TABLE `car_inquiries` AUTO_INCREMENT=1;
ALTER TABLE `personal_access_tokens` AUTO_INCREMENT=1;
ALTER TABLE `company_settings` AUTO_INCREMENT=1;

-- =====================================================
-- SAMPLE DATA INSERTION
-- =====================================================

-- Insert Default Admin User (password: admin123)
INSERT INTO `admin_users` (`name`, `email`, `password`, `role`, `is_active`, `created_at`, `updated_at`) VALUES
('ADJ Admin', '<EMAIL>', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 1, NOW(), NOW());

-- Insert Service Types
INSERT INTO `service_types` (`name`, `description`, `base_price`, `estimated_duration`, `display_order`, `created_at`, `updated_at`) VALUES
('Transmission Rebuilding', 'Complete transmission rebuild and repair services', 2500.00, 480, 1, NOW(), NOW()),
('Engine Repair & Rebuilding', 'Engine diagnostics, repair, and complete rebuilds', 3500.00, 720, 2, NOW(), NOW()),
('Advanced Diagnostics', 'Computer diagnostics and electrical system analysis', 150.00, 60, 3, NOW(), NOW()),
('Brake Service', 'Brake pad replacement, rotor service, and brake system repair', 350.00, 120, 4, NOW(), NOW()),
('Electrical Service', 'Electrical system repair and component replacement', 200.00, 90, 5, NOW(), NOW()),
('Heating & Air Conditioning', 'HVAC system repair and maintenance', 300.00, 120, 6, NOW(), NOW()),
('Suspension & Steering', 'Suspension components and steering system repair', 400.00, 180, 7, NOW(), NOW()),
('Exhaust Service', 'Exhaust system repair and replacement', 250.00, 90, 8, NOW(), NOW()),
('Key Programming', 'Automotive key programming and replacement', 150.00, 30, 9, NOW(), NOW()),
('General Repair', 'General automotive maintenance and repair', 100.00, 60, 10, NOW(), NOW()),
('Fleet Service', 'Commercial fleet maintenance and repair services', 500.00, 240, 11, NOW(), NOW());

-- Insert Company Settings
INSERT INTO `company_settings` (`key`, `value`, `type`, `description`, `is_public`, `created_at`, `updated_at`) VALUES
('company_name', 'ADJ Automotive Repair Services', 'string', 'Company name', 1, NOW(), NOW()),
('company_phone', '(*************', 'string', 'Main phone number', 1, NOW(), NOW()),
('company_email', '<EMAIL>', 'string', 'Main email address', 1, NOW(), NOW()),
('company_address', '125 Chalan Ayuyu Yigo, Guam 96929', 'string', 'Physical address', 1, NOW(), NOW()),
('business_hours', '{"monday":"8:00 AM - 5:00 PM","tuesday":"8:00 AM - 5:00 PM","wednesday":"8:00 AM - 5:00 PM","thursday":"8:00 AM - 5:00 PM","friday":"8:00 AM - 5:00 PM","saturday":"9:00 AM - 2:00 PM","sunday":"Closed"}', 'json', 'Business operating hours', 1, NOW(), NOW()),
('instagram_url', 'https://instagram.com/adjauto', 'string', 'Instagram profile URL', 1, NOW(), NOW()),
('facebook_url', 'https://facebook.com/adjauto', 'string', 'Facebook page URL', 1, NOW(), NOW()),
('tax_rate', '0.04', 'number', 'Local tax rate', 0, NOW(), NOW()),
('max_file_size', '10485760', 'number', 'Maximum file upload size in bytes (10MB)', 0, NOW(), NOW());

COMMIT;
