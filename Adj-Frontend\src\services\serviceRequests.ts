// ADJ Automotive Repair Services - Service Requests Service
// Handles service booking and management

import { apiClient, PaginatedResponse, ApiResponse } from './api';

// Types
export interface ServiceRequest {
  id: number;
  customer_id?: number;
  customer_name: string;
  email: string;
  phone?: string;
  service_type_id?: number;
  service_type: string;
  vehicle_make?: string;
  vehicle_model?: string;
  vehicle_year?: string;
  vehicle_vin?: string;
  description: string;
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  estimated_cost?: number;
  final_cost?: number;
  scheduled_date?: string;
  completed_date?: string;
  assigned_to?: number;
  notes?: string;
  created_at: string;
  updated_at: string;
  customer?: {
    id: number;
    name: string;
    email: string;
    phone?: string;
  };
  service_type_info?: {
    id: number;
    name: string;
    description?: string;
    base_price?: number;
  };
  assigned_to_info?: {
    id: number;
    name: string;
    email: string;
    role: string;
  };
}

export interface CreateServiceRequestData {
  customer_name: string;
  email: string;
  phone?: string;
  service_type: string;
  vehicle_make?: string;
  vehicle_model?: string;
  vehicle_year?: string;
  vehicle_vin?: string;
  description: string;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
}

export interface UpdateServiceRequestData {
  customer_name?: string;
  email?: string;
  phone?: string;
  service_type?: string;
  vehicle_make?: string;
  vehicle_model?: string;
  vehicle_year?: string;
  vehicle_vin?: string;
  description?: string;
  status?: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  estimated_cost?: number;
  final_cost?: number;
  scheduled_date?: string;
  completed_date?: string;
  assigned_to?: number;
  notes?: string;
}

export interface ServiceRequestFilters {
  status?: string;
  priority?: string;
  search?: string;
  assigned_to?: number;
  per_page?: number;
  page?: number;
}

// Service Types
export interface ServiceType {
  id: number;
  name: string;
  description?: string;
  base_price?: number;
  estimated_duration?: number;
  is_active: boolean;
  display_order: number;
}

// Service Requests Service Class
class ServiceRequestsService {
  /**
   * Get service requests list with filtering and pagination (admin only)
   */
  async getServiceRequests(filters: ServiceRequestFilters = {}): Promise<PaginatedResponse<ServiceRequest>> {
    const params = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get<PaginatedResponse<ServiceRequest>>('/service-requests', Object.fromEntries(params));
  }

  /**
   * Get single service request by ID (admin only)
   */
  async getServiceRequest(id: number): Promise<ServiceRequest> {
    const response = await apiClient.get<{ data: ServiceRequest }>(`/service-requests/${id}`);
    return response.data;
  }

  /**
   * Create new service request (public endpoint)
   */
  async createServiceRequest(requestData: CreateServiceRequestData): Promise<ServiceRequest> {
    const response = await apiClient.post<{ data: ServiceRequest; message: string }>('/service-requests', requestData);
    return response.data;
  }

  /**
   * Update service request (admin only)
   */
  async updateServiceRequest(id: number, requestData: UpdateServiceRequestData): Promise<ServiceRequest> {
    const response = await apiClient.put<{ data: ServiceRequest; message: string }>(`/service-requests/${id}`, requestData);
    return response.data;
  }

  /**
   * Delete service request (admin only)
   */
  async deleteServiceRequest(id: number): Promise<void> {
    await apiClient.delete(`/service-requests/${id}`);
  }

  /**
   * Get service requests by status (admin only)
   */
  async getServiceRequestsByStatus(status: string): Promise<ServiceRequest[]> {
    const response = await this.getServiceRequests({ status, per_page: 100 });
    return response.data;
  }

  /**
   * Get pending service requests (admin only)
   */
  async getPendingServiceRequests(): Promise<ServiceRequest[]> {
    return this.getServiceRequestsByStatus('pending');
  }

  /**
   * Get service request statistics (admin only)
   */
  async getServiceRequestStats(): Promise<{
    total: number;
    pending: number;
    confirmed: number;
    in_progress: number;
    completed: number;
    cancelled: number;
  }> {
    const allRequests = await this.getServiceRequests({ per_page: 1000 });
    
    const stats = {
      total: allRequests.total,
      pending: 0,
      confirmed: 0,
      in_progress: 0,
      completed: 0,
      cancelled: 0
    };

    allRequests.data.forEach(request => {
      stats[request.status]++;
    });

    return stats;
  }

  /**
   * Update service request status (admin only)
   */
  async updateServiceRequestStatus(id: number, status: ServiceRequest['status'], notes?: string): Promise<ServiceRequest> {
    const updateData: UpdateServiceRequestData = { status };
    
    if (notes) {
      updateData.notes = notes;
    }

    // Set completed date when marking as completed
    if (status === 'completed') {
      updateData.completed_date = new Date().toISOString();
    }

    return this.updateServiceRequest(id, updateData);
  }

  /**
   * Assign service request to technician (admin only)
   */
  async assignServiceRequest(id: number, assignedTo: number): Promise<ServiceRequest> {
    return this.updateServiceRequest(id, { assigned_to: assignedTo });
  }

  /**
   * Schedule service request (admin only)
   */
  async scheduleServiceRequest(id: number, scheduledDate: string): Promise<ServiceRequest> {
    return this.updateServiceRequest(id, { 
      scheduled_date: scheduledDate,
      status: 'confirmed'
    });
  }
}

// Export singleton instance
export const serviceRequestsService = new ServiceRequestsService();

// Utility functions
export const getStatusColor = (status: ServiceRequest['status']): string => {
  const colors = {
    pending: 'bg-yellow-100 text-yellow-800',
    confirmed: 'bg-blue-100 text-blue-800',
    in_progress: 'bg-purple-100 text-purple-800',
    completed: 'bg-green-100 text-green-800',
    cancelled: 'bg-red-100 text-red-800',
  };
  return colors[status] || 'bg-gray-100 text-gray-800';
};

export const getPriorityColor = (priority: ServiceRequest['priority']): string => {
  const colors = {
    low: 'bg-gray-100 text-gray-800',
    normal: 'bg-blue-100 text-blue-800',
    high: 'bg-orange-100 text-orange-800',
    urgent: 'bg-red-100 text-red-800',
  };
  return colors[priority] || 'bg-gray-100 text-gray-800';
};

export const formatServiceRequestDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

export const getServiceRequestDisplayName = (request: ServiceRequest): string => {
  const vehicle = request.vehicle_make && request.vehicle_model 
    ? `${request.vehicle_year || ''} ${request.vehicle_make} ${request.vehicle_model}`.trim()
    : 'Vehicle';
  
  return `${request.service_type} - ${vehicle}`;
};
