<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Login - ADJ Automotive</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
            width: 200px;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 ADJ Automotive API Test</h1>
        <p>Test the API connection and login functionality</p>

        <!-- Test Credentials -->
        <div class="test-section info">
            <h3>📋 Test Credentials</h3>
            <p><strong>Admin:</strong> <EMAIL> / admin123</p>
            <p><strong>Manager:</strong> <EMAIL> / manager123</p>
            <p><strong>Staff:</strong> <EMAIL> / staff123</p>
        </div>

        <!-- API Status Test -->
        <div class="test-section">
            <h3>🌐 API Status Test</h3>
            <button onclick="testApiStatus()">Test API Status</button>
            <div id="statusResult"></div>
        </div>

        <!-- Login Test -->
        <div class="test-section">
            <h3>🔐 Login Test</h3>
            <div>
                <input type="email" id="loginEmail" placeholder="Email" value="<EMAIL>">
                <input type="password" id="loginPassword" placeholder="Password" value="admin123">
                <button onclick="testLogin()">Test Login</button>
            </div>
            <div id="loginResult"></div>
        </div>

        <!-- Registration Test -->
        <div class="test-section">
            <h3>📝 Registration Test</h3>
            <div>
                <input type="text" id="regName" placeholder="Name" value="Test User">
                <input type="email" id="regEmail" placeholder="Email" value="<EMAIL>">
                <input type="password" id="regPassword" placeholder="Password" value="password123">
                <input type="password" id="regPasswordConfirm" placeholder="Confirm Password" value="password123">
                <select id="regRole">
                    <option value="staff">Staff</option>
                    <option value="manager">Manager</option>
                    <option value="admin">Admin</option>
                </select>
                <button onclick="testRegistration()">Test Registration</button>
            </div>
            <div id="registrationResult"></div>
        </div>

        <!-- Frontend Links -->
        <div class="test-section info">
            <h3>🚀 Frontend Links</h3>
            <p><a href="http://localhost:3000" target="_blank">React Frontend Home</a></p>
            <p><a href="http://localhost:3000/admin/login" target="_blank">React Admin Login</a></p>
            <p><a href="http://localhost:8000/admin/register" target="_blank">Web Registration Form</a></p>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api';

        async function testApiStatus() {
            const result = document.getElementById('statusResult');
            result.innerHTML = '<p>Testing API status...</p>';
            
            try {
                const response = await fetch('http://localhost:8000/');
                const data = await response.json();
                
                result.innerHTML = `
                    <div class="success">
                        <h4>✅ API Status: OK</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                result.innerHTML = `
                    <div class="error">
                        <h4>❌ API Status: Failed</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function testLogin() {
            const result = document.getElementById('loginResult');
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            
            result.innerHTML = '<p>Testing login...</p>';
            
            try {
                const response = await fetch(`${API_BASE}/admin/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    result.innerHTML = `
                        <div class="success">
                            <h4>✅ Login Successful!</h4>
                            <p><strong>User:</strong> ${data.user.name} (${data.user.role})</p>
                            <p><strong>Token:</strong> ${data.token.substring(0, 30)}...</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    result.innerHTML = `
                        <div class="error">
                            <h4>❌ Login Failed</h4>
                            <p>Error: ${data.message || 'Unknown error'}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                result.innerHTML = `
                    <div class="error">
                        <h4>❌ Login Error</h4>
                        <p>Network Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function testRegistration() {
            const result = document.getElementById('registrationResult');
            const name = document.getElementById('regName').value;
            const email = document.getElementById('regEmail').value;
            const password = document.getElementById('regPassword').value;
            const password_confirmation = document.getElementById('regPasswordConfirm').value;
            const role = document.getElementById('regRole').value;
            
            result.innerHTML = '<p>Testing registration...</p>';
            
            try {
                const response = await fetch(`${API_BASE}/admin/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({ name, email, password, password_confirmation, role })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    result.innerHTML = `
                        <div class="success">
                            <h4>✅ Registration Successful!</h4>
                            <p><strong>User:</strong> ${data.user.name} (${data.user.role})</p>
                            <p><strong>Email:</strong> ${data.user.email}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    result.innerHTML = `
                        <div class="error">
                            <h4>❌ Registration Failed</h4>
                            <p>Error: ${data.message || 'Unknown error'}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                result.innerHTML = `
                    <div class="error">
                        <h4>❌ Registration Error</h4>
                        <p>Network Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        // Auto-test API status on page load
        window.onload = function() {
            testApiStatus();
        };
    </script>
</body>
</html>
