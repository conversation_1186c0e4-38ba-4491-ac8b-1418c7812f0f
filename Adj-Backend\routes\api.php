<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\RegisterController;
use App\Http\Controllers\Api\ServiceRequestController;
use App\Http\Controllers\Api\VehicleController;
use App\Http\Controllers\Api\CarInquiryController;
use App\Http\Controllers\Api\CustomerController;
use App\Http\Controllers\Api\CompanySettingController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Authentication Routes
Route::prefix('admin')->group(function () {
    Route::post('/login', [AuthController::class, 'login']);
    Route::post('/register', [RegisterController::class, 'register']); // For testing only

    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::get('/user', [AuthController::class, 'user']);
    });
});

// Public Routes (no authentication required)
Route::get('/vehicles', [VehicleController::class, 'index']);
Route::get('/vehicles/{vehicle}', [VehicleController::class, 'show']);
Route::post('/service-requests', [ServiceRequestController::class, 'store']);
Route::post('/car-inquiries', [CarInquiryController::class, 'store']);
Route::get('/company-settings/public', [CompanySettingController::class, 'public']);

// Protected Routes (require authentication)
Route::middleware('auth:sanctum')->group(function () {
    // Service Requests Management
    Route::apiResource('service-requests', ServiceRequestController::class)->except(['store']);

    // Vehicle Management (Admin only)
    Route::apiResource('vehicles', VehicleController::class)->except(['index', 'show']);
    Route::post('/vehicles/{vehicle}/images', [VehicleController::class, 'uploadImages']);
    Route::delete('/vehicles/{vehicle}/images/{image}', [VehicleController::class, 'deleteImage']);

    // Car Inquiries Management
    Route::apiResource('car-inquiries', CarInquiryController::class)->except(['store']);

    // Customer Management
    Route::apiResource('customers', CustomerController::class);

    // Company Settings Management
    Route::apiResource('company-settings', CompanySettingController::class)->except(['show']);
});
