<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\AdminUser;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create default admin user
        AdminUser::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'ADJ Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'role' => 'admin',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        // Create additional test users
        AdminUser::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'ADJ Manager',
                'email' => '<EMAIL>',
                'password' => Hash::make('manager123'),
                'role' => 'manager',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        AdminUser::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'ADJ Staff',
                'email' => '<EMAIL>',
                'password' => Hash::make('staff123'),
                'role' => 'staff',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        $this->command->info('Admin users created successfully!');
        $this->command->info('Admin: <EMAIL> / admin123');
        $this->command->info('Manager: <EMAIL> / manager123');
        $this->command->info('Staff: <EMAIL> / staff123');
    }
}
