<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ServiceRequest;
use App\Models\Customer;
use App\Models\ServiceType;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class ServiceRequestController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = ServiceRequest::with(['customer', 'serviceType', 'assignedTo']);

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Filter by priority
        if ($request->has('priority')) {
            $query->where('priority', $request->priority);
        }

        // Search functionality
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('customer_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('service_type', 'like', "%{$search}%")
                  ->orWhere('vehicle_make', 'like', "%{$search}%")
                  ->orWhere('vehicle_model', 'like', "%{$search}%");
            });
        }

        // Sort by created_at desc by default
        $query->orderBy('created_at', 'desc');

        $serviceRequests = $query->paginate($request->get('per_page', 15));

        return response()->json($serviceRequests);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'customer_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'service_type' => 'required|string|max:255',
            'vehicle_make' => 'nullable|string|max:100',
            'vehicle_model' => 'nullable|string|max:100',
            'vehicle_year' => 'nullable|string|max:4',
            'vehicle_vin' => 'nullable|string|max:17',
            'description' => 'required|string',
            'priority' => 'nullable|in:low,normal,high,urgent',
        ]);

        // Find or create customer
        $customer = Customer::firstOrCreate(
            ['email' => $validated['email']],
            [
                'name' => $validated['customer_name'],
                'phone' => $validated['phone'] ?? null,
            ]
        );

        // Find service type if it exists
        $serviceType = ServiceType::where('name', $validated['service_type'])->first();

        $serviceRequest = ServiceRequest::create([
            'customer_id' => $customer->id,
            'customer_name' => $validated['customer_name'],
            'email' => $validated['email'],
            'phone' => $validated['phone'] ?? null,
            'service_type_id' => $serviceType?->id,
            'service_type' => $validated['service_type'],
            'vehicle_make' => $validated['vehicle_make'] ?? null,
            'vehicle_model' => $validated['vehicle_model'] ?? null,
            'vehicle_year' => $validated['vehicle_year'] ?? null,
            'vehicle_vin' => $validated['vehicle_vin'] ?? null,
            'description' => $validated['description'],
            'priority' => $validated['priority'] ?? 'normal',
            'status' => 'pending',
        ]);

        $serviceRequest->load(['customer', 'serviceType']);

        return response()->json([
            'message' => 'Service request created successfully',
            'data' => $serviceRequest
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(ServiceRequest $serviceRequest)
    {
        $serviceRequest->load(['customer', 'serviceType', 'assignedTo']);

        return response()->json([
            'data' => $serviceRequest
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ServiceRequest $serviceRequest)
    {
        $validated = $request->validate([
            'customer_name' => 'sometimes|string|max:255',
            'email' => 'sometimes|email|max:255',
            'phone' => 'nullable|string|max:20',
            'service_type' => 'sometimes|string|max:255',
            'vehicle_make' => 'nullable|string|max:100',
            'vehicle_model' => 'nullable|string|max:100',
            'vehicle_year' => 'nullable|string|max:4',
            'vehicle_vin' => 'nullable|string|max:17',
            'description' => 'sometimes|string',
            'status' => 'sometimes|in:pending,confirmed,in_progress,completed,cancelled',
            'priority' => 'sometimes|in:low,normal,high,urgent',
            'estimated_cost' => 'nullable|numeric|min:0',
            'final_cost' => 'nullable|numeric|min:0',
            'scheduled_date' => 'nullable|date',
            'completed_date' => 'nullable|date',
            'assigned_to' => 'nullable|exists:admin_users,id',
            'notes' => 'nullable|string',
        ]);

        // Update service type if changed
        if (isset($validated['service_type'])) {
            $serviceType = ServiceType::where('name', $validated['service_type'])->first();
            $validated['service_type_id'] = $serviceType?->id;
        }

        $serviceRequest->update($validated);
        $serviceRequest->load(['customer', 'serviceType', 'assignedTo']);

        return response()->json([
            'message' => 'Service request updated successfully',
            'data' => $serviceRequest
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ServiceRequest $serviceRequest)
    {
        $serviceRequest->delete();

        return response()->json([
            'message' => 'Service request deleted successfully'
        ]);
    }
}
