<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Vehicle;
use App\Models\VehicleImage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class VehicleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Vehicle::with(['images', 'primaryImage']);

        // Filter by status (default to available for public)
        $status = $request->get('status', 'available');
        if ($status !== 'all') {
            $query->where('status', $status);
        }

        // Filter by category
        if ($request->has('category')) {
            $query->where('category', $request->category);
        }

        // Filter by featured
        if ($request->has('featured')) {
            $query->where('featured', $request->boolean('featured'));
        }

        // Search functionality
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('make', 'like', "%{$search}%")
                  ->orWhere('model', 'like', "%{$search}%")
                  ->orWhere('year', 'like', "%{$search}%");
            });
        }

        // Price range filter
        if ($request->has('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }
        if ($request->has('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }

        // Year range filter
        if ($request->has('min_year')) {
            $query->where('year', '>=', $request->min_year);
        }
        if ($request->has('max_year')) {
            $query->where('year', '<=', $request->max_year);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $vehicles = $query->paginate($request->get('per_page', 12));

        return response()->json($vehicles);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'make' => 'required|string|max:100',
            'model' => 'required|string|max:100',
            'year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            'price' => 'required|numeric|min:0',
            'mileage' => 'nullable|string|max:50',
            'engine' => 'nullable|string|max:255',
            'transmission' => 'nullable|string|max:100',
            'drivetrain' => 'nullable|string|max:50',
            'fuel_type' => 'nullable|string|max:50',
            'exterior_color' => 'nullable|string|max:50',
            'interior_color' => 'nullable|string|max:50',
            'vin' => 'nullable|string|max:17|unique:vehicles,vin',
            'stock_number' => 'nullable|string|max:50|unique:vehicles,stock_number',
            'description' => 'nullable|string',
            'features' => 'nullable|array',
            'category' => 'nullable|in:sedan,suv,truck,sports,luxury,compact,convertible',
            'featured' => 'boolean',
            'condition' => 'required|in:new,used,certified',
        ]);

        $vehicle = Vehicle::create($validated);
        $vehicle->load(['images', 'primaryImage']);

        return response()->json([
            'message' => 'Vehicle created successfully',
            'data' => $vehicle
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Vehicle $vehicle)
    {
        // Increment view count
        $vehicle->increment('views');

        $vehicle->load(['images', 'primaryImage']);

        return response()->json([
            'data' => $vehicle
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Vehicle $vehicle)
    {
        $validated = $request->validate([
            'title' => 'sometimes|string|max:255',
            'make' => 'sometimes|string|max:100',
            'model' => 'sometimes|string|max:100',
            'year' => 'sometimes|integer|min:1900|max:' . (date('Y') + 1),
            'price' => 'sometimes|numeric|min:0',
            'mileage' => 'nullable|string|max:50',
            'engine' => 'nullable|string|max:255',
            'transmission' => 'nullable|string|max:100',
            'drivetrain' => 'nullable|string|max:50',
            'fuel_type' => 'nullable|string|max:50',
            'exterior_color' => 'nullable|string|max:50',
            'interior_color' => 'nullable|string|max:50',
            'vin' => 'nullable|string|max:17|unique:vehicles,vin,' . $vehicle->id,
            'stock_number' => 'nullable|string|max:50|unique:vehicles,stock_number,' . $vehicle->id,
            'description' => 'nullable|string',
            'features' => 'nullable|array',
            'status' => 'sometimes|in:available,pending_sale,sold,maintenance',
            'category' => 'nullable|in:sedan,suv,truck,sports,luxury,compact,convertible',
            'featured' => 'sometimes|boolean',
            'condition' => 'sometimes|in:new,used,certified',
        ]);

        $vehicle->update($validated);
        $vehicle->load(['images', 'primaryImage']);

        return response()->json([
            'message' => 'Vehicle updated successfully',
            'data' => $vehicle
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Vehicle $vehicle)
    {
        // Delete associated images from storage
        foreach ($vehicle->images as $image) {
            if (Storage::exists($image->image_path)) {
                Storage::delete($image->image_path);
            }
        }

        $vehicle->delete();

        return response()->json([
            'message' => 'Vehicle deleted successfully'
        ]);
    }

    /**
     * Upload images for a vehicle.
     */
    public function uploadImages(Request $request, Vehicle $vehicle)
    {
        $request->validate([
            'images' => 'required|array|max:10',
            'images.*' => 'image|mimes:jpeg,png,jpg,webp|max:5120', // 5MB max
            'primary_image_index' => 'nullable|integer|min:0',
        ]);

        $uploadedImages = [];
        $images = $request->file('images');
        $primaryIndex = $request->get('primary_image_index', 0);

        foreach ($images as $index => $image) {
            $filename = time() . '_' . $index . '.' . $image->getClientOriginalExtension();
            $path = $image->storeAs('vehicles/' . $vehicle->id, $filename, 'public');

            $vehicleImage = VehicleImage::create([
                'vehicle_id' => $vehicle->id,
                'image_url' => Storage::url($path),
                'image_path' => $path,
                'alt_text' => $vehicle->title . ' - Image ' . ($index + 1),
                'is_primary' => $index === $primaryIndex,
                'display_order' => $index,
                'file_size' => $image->getSize(),
                'mime_type' => $image->getMimeType(),
            ]);

            $uploadedImages[] = $vehicleImage;
        }

        // Ensure only one primary image
        if ($primaryIndex >= 0 && $primaryIndex < count($images)) {
            VehicleImage::where('vehicle_id', $vehicle->id)
                ->where('id', '!=', $uploadedImages[$primaryIndex]->id)
                ->update(['is_primary' => false]);
        }

        return response()->json([
            'message' => 'Images uploaded successfully',
            'data' => $uploadedImages
        ]);
    }

    /**
     * Delete a specific vehicle image.
     */
    public function deleteImage(Vehicle $vehicle, VehicleImage $image)
    {
        if ($image->vehicle_id !== $vehicle->id) {
            return response()->json([
                'message' => 'Image does not belong to this vehicle'
            ], 403);
        }

        // Delete from storage
        if (Storage::exists($image->image_path)) {
            Storage::delete($image->image_path);
        }

        $image->delete();

        return response()->json([
            'message' => 'Image deleted successfully'
        ]);
    }
}
