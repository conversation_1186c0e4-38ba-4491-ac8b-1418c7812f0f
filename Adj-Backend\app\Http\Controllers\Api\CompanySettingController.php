<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\CompanySetting;
use Illuminate\Http\Request;

class CompanySettingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = CompanySetting::query();

        // Filter by public settings only if requested
        if ($request->has('public_only')) {
            $query->where('is_public', true);
        }

        $settings = $query->orderBy('key')->get();

        return response()->json([
            'data' => $settings
        ]);
    }

    /**
     * Get public settings only (no authentication required).
     */
    public function public()
    {
        $settings = CompanySetting::where('is_public', true)
            ->orderBy('key')
            ->get()
            ->keyBy('key');

        return response()->json([
            'data' => $settings
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'key' => 'required|string|max:255|unique:company_settings,key',
            'value' => 'nullable|string',
            'type' => 'required|in:string,number,boolean,json',
            'description' => 'nullable|string',
            'is_public' => 'boolean',
        ]);

        $setting = CompanySetting::create($validated);

        return response()->json([
            'message' => 'Company setting created successfully',
            'data' => $setting
        ], 201);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, CompanySetting $companySetting)
    {
        $validated = $request->validate([
            'key' => 'sometimes|string|max:255|unique:company_settings,key,' . $companySetting->id,
            'value' => 'nullable|string',
            'type' => 'sometimes|in:string,number,boolean,json',
            'description' => 'nullable|string',
            'is_public' => 'sometimes|boolean',
        ]);

        $companySetting->update($validated);

        return response()->json([
            'message' => 'Company setting updated successfully',
            'data' => $companySetting
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CompanySetting $companySetting)
    {
        $companySetting->delete();

        return response()->json([
            'message' => 'Company setting deleted successfully'
        ]);
    }
}
