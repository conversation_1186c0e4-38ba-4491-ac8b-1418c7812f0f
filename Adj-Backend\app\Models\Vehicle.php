<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Vehicle extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'make',
        'model',
        'year',
        'price',
        'mileage',
        'engine',
        'transmission',
        'drivetrain',
        'fuel_type',
        'exterior_color',
        'interior_color',
        'vin',
        'stock_number',
        'description',
        'features',
        'status',
        'category',
        'featured',
        'views',
        'condition',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'year' => 'integer',
        'featured' => 'boolean',
        'views' => 'integer',
        'features' => 'array',
    ];

    // Relationships
    public function images()
    {
        return $this->hasMany(VehicleImage::class)->orderBy('display_order');
    }

    public function primaryImage()
    {
        return $this->hasOne(VehicleImage::class)->where('is_primary', true);
    }

    public function carInquiries()
    {
        return $this->hasMany(CarInquiry::class);
    }

    // Scopes
    public function scopeAvailable($query)
    {
        return $query->where('status', 'available');
    }

    public function scopeFeatured($query)
    {
        return $query->where('featured', true);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    // Accessors
    public function getFormattedPriceAttribute()
    {
        return '$' . number_format($this->price, 2);
    }
}
