import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { SearchIcon, EyeIcon, CheckIcon, XIcon, ClockIcon, ArrowUpDownIcon, FilterIcon, ChevronLeftIcon, ChevronRightIcon, PhoneIcon, MailIcon, MessageSquareIcon, CalendarIcon, FileTextIcon, MoreHorizontalIcon, ArrowLeftIcon, MenuIcon } from 'lucide-react';
import AdminSidebar from '../components/AdminSidebar';
import { apiClient } from '../services/api';

// Move getStatusColor function outside components so both can access it
const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'completed':
      return 'bg-green-100 text-green-800';
    case 'in progress':
      return 'bg-blue-100 text-blue-800';
    case 'scheduled':
      return 'bg-purple-100 text-purple-800';
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'cancelled':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const AdminServiceRequests = () => {
  const [searchParams] = useSearchParams();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [sortField, setSortField] = useState('requestDate');
  const [sortDirection, setSortDirection] = useState('desc');
  const [selectedRequest, setSelectedRequest] = useState(null);

  // Handle URL parameters for pre-filtering
  useEffect(() => {
    const status = searchParams.get('status');
    const view = searchParams.get('view');
    
    if (status) {
      setStatusFilter(status);
    }
    
    if (view === 'schedule') {
      // For schedule view, sort by scheduled date to show appointments chronologically
      setSortField('scheduledDate');
      setSortDirection('asc');
    }
  }, [searchParams]);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  // State for service requests
  const [serviceRequests, setServiceRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch service requests from API
  useEffect(() => {
    const fetchServiceRequests = async () => {
      try {
        setLoading(true);
        const token = localStorage.getItem('admin_token');
        if (!token) {
          setError('Authentication required');
          return;
        }

        const response = await apiClient.get('/service-requests', {}, {
          headers: { Authorization: `Bearer ${token}` }
        });
        setServiceRequests(response.data?.data || []);
      } catch (err) {
        console.error('Error fetching service requests:', err);
        setError('Failed to load service requests');
      } finally {
        setLoading(false);
      }
    };

    fetchServiceRequests();
  }, []);

  // Filter requests based on search query and status
  const filteredRequests = serviceRequests.filter(request => {
    if (!request) return false;
    const searchLower = searchQuery.toLowerCase();
    const matchesSearch =
      request.customer_name?.toLowerCase().includes(searchLower) ||
      request.id?.toString().toLowerCase().includes(searchLower) ||
      `${request.vehicle_make} ${request.vehicle_model}`.toLowerCase().includes(searchLower) ||
      request.service_type?.toLowerCase().includes(searchLower);
    const matchesStatus = statusFilter === 'all' || request.status?.toLowerCase() === statusFilter.toLowerCase();
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex h-screen bg-gray-100">
        <aside className="bg-[#1e3a5f] text-white w-64 flex-shrink-0 hidden md:block">
          <AdminSidebar activePage="service-requests" />
        </aside>
        <div className="flex-1 flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-screen bg-gray-100">
        <aside className="bg-[#1e3a5f] text-white w-64 flex-shrink-0 hidden md:block">
          <AdminSidebar activePage="service-requests" />
        </aside>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Error Loading Service Requests</h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }


  // Sort requests
  const sortedRequests = [...filteredRequests].sort((a, b) => {
    if (sortField === 'requestDate') {
      return sortDirection === 'asc'
        ? new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        : new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
    }
    if (sortField === 'scheduledDate') {
      // Handle null scheduled dates
      if (!a.scheduled_date) return sortDirection === 'asc' ? 1 : -1;
      if (!b.scheduled_date) return sortDirection === 'asc' ? -1 : 1;
      return sortDirection === 'asc'
        ? new Date(a.scheduled_date).getTime() - new Date(b.scheduled_date).getTime()
        : new Date(b.scheduled_date).getTime() - new Date(a.scheduled_date).getTime();
    }
    if (sortField === 'customer') {
      return sortDirection === 'asc' ? a.customer_name.localeCompare(b.customer_name) : b.customer_name.localeCompare(a.customer_name);
    }
    return 0;
  });
  // Pagination
  const itemsPerPage = 10;
  const totalPages = Math.ceil(sortedRequests.length / itemsPerPage);
  const paginatedRequests = sortedRequests.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage);
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };
  const handleViewRequest = (request: any) => {
    setSelectedRequest(request);
  };
  if (selectedRequest) {
    return <ServiceRequestDetail request={selectedRequest} onBack={() => setSelectedRequest(null)} />;
  }
  return <div className="flex h-screen bg-gray-100">
      {/* Sidebar for desktop */}
      <aside className="bg-[#1e3a5f] text-white w-64 flex-shrink-0 hidden md:block">
        <AdminSidebar activePage="service-requests" />
      </aside>

      {/* Mobile sidebar */}
      <div className={`fixed inset-0 bg-gray-800 bg-opacity-75 z-40 md:hidden transition-opacity duration-200 ${sidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}`} onClick={toggleSidebar}></div>

      <aside className={`fixed top-0 left-0 h-full bg-[#1e3a5f] text-white w-64 z-50 transform transition-transform duration-200 ease-in-out md:hidden ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}>
        <div className="absolute top-4 right-4">
          <button className="text-white p-2" onClick={toggleSidebar}>
            <XIcon className="h-6 w-6" />
          </button>
        </div>
        <AdminSidebar activePage="service-requests" />
      </aside>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top header */}
        <header className="bg-white shadow-sm z-10">
          <div className="px-4 py-4 flex justify-between items-center">
            <div className="flex items-center md:hidden">
              <button className="text-gray-600 p-2" onClick={toggleSidebar}>
                <MenuIcon className="h-6 w-6" />
              </button>
            </div>
            <h1 className="text-xl font-semibold text-gray-800">
              Service Requests
            </h1>
            <div></div>
          </div>
        </header>

        {/* Main content area */}
        <main className="flex-1 overflow-y-auto bg-gray-100 p-4">
          <div className="bg-white rounded-xl shadow-md p-4 mb-6">
            <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
              <div className="flex-1">
                <div className="relative">
                  <input type="text" placeholder="Search by customer, ID, vehicle..." value={searchQuery} onChange={e => setSearchQuery(e.target.value)} className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
                  <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="flex items-center">
                  <span className="text-gray-600 mr-2">Status:</span>
                  <select value={statusFilter} onChange={e => setStatusFilter(e.target.value)} className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    <option value="all">All</option>
                    <option value="pending">Pending</option>
                    <option value="scheduled">Scheduled</option>
                    <option value="in progress">In Progress</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>
                <button className="p-2 text-gray-600 hover:text-gray-800">
                  <FilterIcon className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>

          {/* Schedule View Indicator */}
          {searchParams.get('view') === 'schedule' && (
            <div className="bg-blue-50 border-l-4 border-blue-500 p-4 mb-6 rounded-r-lg">
              <div className="flex items-center">
                <CalendarIcon className="h-5 w-5 text-blue-600 mr-2" />
                <div>
                  <p className="text-sm font-medium text-blue-800">
                    Schedule View Active
                  </p>
                  <p className="text-xs text-blue-600 mt-1">
                    Showing {statusFilter === 'all' ? 'all' : statusFilter} appointments sorted by scheduled date
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="bg-white rounded-xl shadow-md overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="bg-gray-100 border-b border-gray-200">
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      ID
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <button onClick={() => handleSort('customer')} className="flex items-center">
                        Customer
                        <ArrowUpDownIcon className="h-4 w-4 ml-1" />
                      </button>
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Service
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Vehicle
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <button onClick={() => handleSort('requestDate')} className="flex items-center">
                        Request Date
                        <ArrowUpDownIcon className="h-4 w-4 ml-1" />
                      </button>
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <button onClick={() => handleSort('scheduledDate')} className="flex items-center">
                        Scheduled Date
                        <ArrowUpDownIcon className="h-4 w-4 ml-1" />
                      </button>
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {paginatedRequests.map(request => <tr key={request.id} className="hover:bg-gray-50">
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-blue-600">
                        {request.id}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {request.customer_name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {request.phone}
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        {request.service_type}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        {request.vehicle_make} {request.vehicle_model} {request.vehicle_year}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(request.status)}`}>
                          {request.status}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(request.created_at).toLocaleDateString()}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                        {request.scheduled_date ? new Date(request.scheduled_date).toLocaleDateString() : '-'}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button onClick={() => handleViewRequest(request)} className="text-blue-600 hover:text-blue-900">
                          <EyeIcon className="h-5 w-5" />
                        </button>
                      </td>
                    </tr>)}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200">
                <div className="flex-1 flex justify-between items-center">
                  <p className="text-sm text-gray-700">
                    Showing{' '}
                    <span className="font-medium">
                      {(currentPage - 1) * itemsPerPage + 1}
                    </span>{' '}
                    to{' '}
                    <span className="font-medium">
                      {Math.min(currentPage * itemsPerPage, sortedRequests.length)}
                    </span>{' '}
                    of{' '}
                    <span className="font-medium">{sortedRequests.length}</span>{' '}
                    results
                  </p>
                  <div className="flex space-x-2">
                    <button onClick={() => setCurrentPage(currentPage - 1)} disabled={currentPage === 1} className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md bg-white ${currentPage === 1 ? 'text-gray-300' : 'text-gray-700 hover:bg-gray-50'}`}>
                      <ChevronLeftIcon className="h-4 w-4" />
                    </button>
                    <button onClick={() => setCurrentPage(currentPage + 1)} disabled={currentPage === totalPages} className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md bg-white ${currentPage === totalPages ? 'text-gray-300' : 'text-gray-700 hover:bg-gray-50'}`}>
                      <ChevronRightIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>}
          </div>
        </main>
      </div>
    </div>;
};
const ServiceRequestDetail = ({
  request,
  onBack
}: {
  request: any;
  onBack: () => void;
}) => {
  const [status, setStatus] = useState(request.status);
  const [notes, setNotes] = useState(request.notes || '');
  const [estimatedCost, setEstimatedCost] = useState(request.estimated_cost || '');
  const [finalCost, setFinalCost] = useState(request.final_cost || '');
  const [assignedTo, setAssignedTo] = useState(request.assigned_to || '');
  const [scheduledDate, setScheduledDate] = useState(request.scheduled_date || '');
  const statusOptions = ['Pending', 'Scheduled', 'In Progress', 'Completed', 'Cancelled'];
  const technicians = ['Adam Johnson', 'Sarah Williams', 'David Chen', 'Michael Chen', 'Jennifer Lee'];
  const handleUpdateRequest = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, this would send data to your backend
    console.log('Request updated:', {
      id: request.id,
      status,
      notes,
      estimatedCost,
      finalCost,
      assignedTo,
      scheduledDate
    });
    onBack();
  };
  return <div className="flex h-screen bg-gray-100">
      {/* Sidebar for desktop */}
      <aside className="bg-[#1e3a5f] text-white w-64 flex-shrink-0 hidden md:block">
        <AdminSidebar activePage="service-requests" />
      </aside>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <main className="flex-1 overflow-y-auto bg-gray-100 p-6">
          <button onClick={onBack} className="flex items-center text-blue-600 hover:text-blue-800 mb-6">
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Service Requests
          </button>

          <div className="bg-white rounded-xl shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <div>
                <h2 className="text-xl font-bold text-gray-900">
                  Service Request: {request.id}
                </h2>
                <p className="text-gray-600">
                  {request.service_type} for {request.vehicle_make} {request.vehicle_model} {request.vehicle_year}
                </p>
              </div>
              <span className={`px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full ${getStatusColor(request.status)}`}>
                {request.status}
              </span>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Customer Information */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Customer Information
                  </h3>
                  <div className="space-y-3">
                    <div>
                      <p className="text-gray-500 text-sm">Name</p>
                      <p className="font-medium">{request.customer_name}</p>
                    </div>
                    <div className="flex items-center">
                      <PhoneIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <a href={`tel:${request.phone}`} className="text-blue-600 hover:text-blue-800">
                        {request.phone}
                      </a>
                    </div>
                    <div className="flex items-center">
                      <MailIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <a href={`mailto:${request.email}`} className="text-blue-600 hover:text-blue-800">
                        {request.email}
                      </a>
                    </div>
                  </div>
                  <div className="mt-4 pt-4 border-t border-gray-200 flex space-x-2">
                    <button className="p-2 text-blue-600 hover:text-blue-800 rounded-full hover:bg-blue-50">
                      <PhoneIcon className="h-5 w-5" />
                    </button>
                    <button className="p-2 text-blue-600 hover:text-blue-800 rounded-full hover:bg-blue-50">
                      <MailIcon className="h-5 w-5" />
                    </button>
                    <button className="p-2 text-blue-600 hover:text-blue-800 rounded-full hover:bg-blue-50">
                      <MessageSquareIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>

                {/* Request Details */}
                <div className="md:col-span-2 bg-gray-50 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Request Details
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-gray-500 text-sm">Service Type</p>
                      <p className="font-medium">{request.service_type}</p>
                    </div>
                    <div>
                      <p className="text-gray-500 text-sm">Vehicle</p>
                      <p className="font-medium">{request.vehicle_make} {request.vehicle_model} {request.vehicle_year}</p>
                    </div>
                    <div>
                      <p className="text-gray-500 text-sm">Request Date</p>
                      <div className="flex items-center">
                        <CalendarIcon className="h-4 w-4 text-gray-400 mr-2" />
                        <p>{new Date(request.created_at).toLocaleDateString()}</p>
                      </div>
                    </div>
                    <div>
                      <p className="text-gray-500 text-sm">Scheduled Date</p>
                      <div className="flex items-center">
                        <ClockIcon className="h-4 w-4 text-gray-400 mr-2" />
                        <p>{request.scheduled_date ? new Date(request.scheduled_date).toLocaleDateString() : 'Not scheduled'}</p>
                      </div>
                    </div>
                    {request.completionDate && <div>
                        <p className="text-gray-500 text-sm">Completion Date</p>
                        <div className="flex items-center">
                          <CheckIcon className="h-4 w-4 text-green-500 mr-2" />
                          <p>{request.completionDate}</p>
                        </div>
                      </div>}
                    {request.assignedTo && <div>
                        <p className="text-gray-500 text-sm">Assigned To</p>
                        <p className="font-medium">{request.assignedTo}</p>
                      </div>}
                  </div>
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <p className="text-gray-500 text-sm mb-2">Description</p>
                    <p className="bg-white p-3 rounded border border-gray-200">
                      {request.description}
                    </p>
                  </div>
                  {request.notes && <div className="mt-4 pt-4 border-t border-gray-200">
                      <p className="text-gray-500 text-sm mb-2">Notes</p>
                      <p className="bg-white p-3 rounded border border-gray-200">
                        {request.notes}
                      </p>
                    </div>}
                  <div className="mt-4 pt-4 border-t border-gray-200 flex flex-wrap gap-4">
                    {request.estimated_cost && <div>
                        <p className="text-gray-500 text-sm">Estimated Cost</p>
                        <p className="font-semibold text-lg">
                          ${parseFloat(request.estimated_cost).toLocaleString()}
                        </p>
                      </div>}
                    {request.final_cost && <div>
                        <p className="text-gray-500 text-sm">Final Cost</p>
                        <p className="font-semibold text-lg text-blue-700">
                          ${parseFloat(request.final_cost).toLocaleString()}
                        </p>
                      </div>}
                  </div>
                </div>

                {/* Update Form */}
                <div className="md:col-span-3">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Update Service Request
                  </h3>
                  <form onSubmit={handleUpdateRequest}>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                      <div>
                        <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                          Status
                        </label>
                        <select id="status" value={status} onChange={e => setStatus(e.target.value)} className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                          {statusOptions.map(option => <option key={option} value={option}>
                              {option}
                            </option>)}
                        </select>
                      </div>
                      <div>
                        <label htmlFor="assignedTo" className="block text-sm font-medium text-gray-700 mb-1">
                          Assigned To
                        </label>
                        <select id="assignedTo" value={assignedTo} onChange={e => setAssignedTo(e.target.value)} className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                          <option value="">Select Technician</option>
                          {technicians.map(tech => <option key={tech} value={tech}>
                              {tech}
                            </option>)}
                        </select>
                      </div>
                      <div>
                        <label htmlFor="scheduledDate" className="block text-sm font-medium text-gray-700 mb-1">
                          Scheduled Date
                        </label>
                        <input type="date" id="scheduledDate" value={scheduledDate} onChange={e => setScheduledDate(e.target.value)} className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
                      </div>
                      <div>
                        <label htmlFor="estimatedCost" className="block text-sm font-medium text-gray-700 mb-1">
                          Estimated Cost
                        </label>
                        <input type="text" id="estimatedCost" value={estimatedCost} onChange={e => setEstimatedCost(e.target.value)} placeholder="$0.00" className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
                      </div>
                      <div>
                        <label htmlFor="finalCost" className="block text-sm font-medium text-gray-700 mb-1">
                          Final Cost
                        </label>
                        <input type="text" id="finalCost" value={finalCost} onChange={e => setFinalCost(e.target.value)} placeholder="$0.00" className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
                      </div>
                    </div>
                    <div className="mb-6">
                      <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                        Notes
                      </label>
                      <textarea id="notes" value={notes} onChange={e => setNotes(e.target.value)} rows={4} className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Add notes about the service request..."></textarea>
                    </div>
                    <div className="flex justify-between">
                      <div className="flex space-x-2">
                        <button type="button" className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 flex items-center">
                          <FileTextIcon className="h-5 w-5 mr-2" />
                          Generate Invoice
                        </button>
                        <button type="button" className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 flex items-center">
                          <MessageSquareIcon className="h-5 w-5 mr-2" />
                          Message Customer
                        </button>
                        <div className="relative">
                          <button type="button" className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                            <MoreHorizontalIcon className="h-5 w-5" />
                          </button>
                        </div>
                      </div>
                      <div>
                        <button type="submit" className="px-6 py-2 bg-[#1e3a5f] hover:bg-blue-800 text-white rounded-lg transition-colors">
                          Update Request
                        </button>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>;
};
export default AdminServiceRequests;