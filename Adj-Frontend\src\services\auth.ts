// ADJ Automotive Repair Services - Authentication Service
// Handles admin authentication with Laravel Sanctum backend

import { apiClient, TokenManager, ApiResponse } from './api';

// Types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface User {
  id: number;
  name: string;
  email: string;
  role: 'admin' | 'manager' | 'staff';
  last_login_at?: string;
}

export interface LoginResponse {
  message: string;
  user: User;
  token: string;
}

// Authentication Service Class
class AuthService {
  /**
   * Login admin user
   */
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      const response = await apiClient.post<LoginResponse>('/admin/login', credentials);
      
      // Store token and user data
      TokenManager.setToken(response.token);
      TokenManager.setUser(response.user);
      
      return response;
    } catch (error) {
      // Clear any existing auth data on login failure
      TokenManager.removeToken();
      throw error;
    }
  }

  /**
   * Logout admin user
   */
  async logout(): Promise<void> {
    try {
      await apiClient.post('/admin/logout');
    } catch (error) {
      // Log error but don't throw - we want to clear local data regardless
      console.error('Logout API call failed:', error);
    } finally {
      // Always clear local auth data
      TokenManager.removeToken();
    }
  }

  /**
   * Get current user info
   */
  async getCurrentUser(): Promise<User> {
    const response = await apiClient.get<{ user: User }>('/admin/user');
    
    // Update stored user data
    TokenManager.setUser(response.user);
    
    return response.user;
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return !!TokenManager.getToken();
  }

  /**
   * Get current user from storage
   */
  getCurrentUserFromStorage(): User | null {
    return TokenManager.getUser();
  }

  /**
   * Check if user has specific role
   */
  hasRole(role: string): boolean {
    const user = this.getCurrentUserFromStorage();
    return user?.role === role;
  }

  /**
   * Check if user is admin
   */
  isAdmin(): boolean {
    return this.hasRole('admin');
  }

  /**
   * Check if user is manager or admin
   */
  isManagerOrAdmin(): boolean {
    const user = this.getCurrentUserFromStorage();
    return user?.role === 'admin' || user?.role === 'manager';
  }

  /**
   * Refresh user data from server
   */
  async refreshUser(): Promise<User | null> {
    if (!this.isAuthenticated()) {
      return null;
    }

    try {
      return await this.getCurrentUser();
    } catch (error) {
      // If refresh fails, user might be logged out
      TokenManager.removeToken();
      return null;
    }
  }

  /**
   * Initialize auth state (call on app startup)
   */
  async initialize(): Promise<User | null> {
    if (!this.isAuthenticated()) {
      return null;
    }

    try {
      // Verify token is still valid by fetching user data
      return await this.getCurrentUser();
    } catch (error) {
      // Token is invalid, clear auth data
      TokenManager.removeToken();
      return null;
    }
  }
}

// Export singleton instance
export const authService = new AuthService();

// Auth Hook for React components
export const useAuthStatus = () => {
  const isAuthenticated = authService.isAuthenticated();
  const user = authService.getCurrentUserFromStorage();
  
  return {
    isAuthenticated,
    user,
    isAdmin: authService.isAdmin(),
    isManagerOrAdmin: authService.isManagerOrAdmin(),
  };
};

// Auth utilities
export const requireAuth = (): User => {
  const user = authService.getCurrentUserFromStorage();
  if (!user || !authService.isAuthenticated()) {
    throw new Error('Authentication required');
  }
  return user;
};

export const requireRole = (role: string): User => {
  const user = requireAuth();
  if (user.role !== role) {
    throw new Error(`Role '${role}' required`);
  }
  return user;
};

export const requireAdminRole = (): User => {
  return requireRole('admin');
};
