<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ADJ Automotive API Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; width: 200px; }
    </style>
</head>
<body>
    <h1>🔐 ADJ Automotive API Authentication Test</h1>
    
    <div class="test-section">
        <h3>1. Test Public Endpoints (No Auth Required)</h3>
        <button onclick="testPublicEndpoints()">Test Public APIs</button>
        <div id="public-results"></div>
    </div>

    <div class="test-section">
        <h3>2. Admin Login Test</h3>
        <div>
            <input type="email" id="email" placeholder="Email" value="<EMAIL>">
            <input type="password" id="password" placeholder="Password" value="admin123">
            <button onclick="testLogin()">Login</button>
        </div>
        <div id="login-results"></div>
    </div>

    <div class="test-section">
        <h3>3. Test Protected Endpoints (Auth Required)</h3>
        <button onclick="testProtectedEndpoints()">Test Protected APIs</button>
        <div id="protected-results"></div>
    </div>

    <script>
        let authToken = null;

        async function testPublicEndpoints() {
            const results = document.getElementById('public-results');
            results.innerHTML = '<p>Testing public endpoints...</p>';
            
            try {
                // Test company settings
                const settingsResponse = await fetch('/api/company-settings/public');
                const settings = await settingsResponse.json();
                
                // Test vehicles
                const vehiclesResponse = await fetch('/api/vehicles');
                const vehicles = await vehiclesResponse.json();
                
                results.innerHTML = `
                    <div class="success">
                        <h4>✅ Public Endpoints Working!</h4>
                        <p><strong>Company Settings:</strong> ${Object.keys(settings.data || {}).length} settings loaded</p>
                        <p><strong>Vehicles:</strong> ${vehicles.data?.length || 0} vehicles found</p>
                        <pre>${JSON.stringify(settings, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                results.innerHTML = `
                    <div class="error">
                        <h4>❌ Public Endpoints Failed</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function testLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const results = document.getElementById('login-results');
            
            results.innerHTML = '<p>Testing login...</p>';
            
            try {
                const response = await fetch('/api/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    authToken = data.token;
                    results.innerHTML = `
                        <div class="success">
                            <h4>✅ Login Successful!</h4>
                            <p><strong>User:</strong> ${data.user.name} (${data.user.role})</p>
                            <p><strong>Token:</strong> ${data.token.substring(0, 20)}...</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    results.innerHTML = `
                        <div class="error">
                            <h4>❌ Login Failed</h4>
                            <p>Error: ${data.message || 'Unknown error'}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                results.innerHTML = `
                    <div class="error">
                        <h4>❌ Login Request Failed</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function testProtectedEndpoints() {
            const results = document.getElementById('protected-results');
            
            if (!authToken) {
                results.innerHTML = `
                    <div class="error">
                        <h4>❌ No Auth Token</h4>
                        <p>Please login first to get an authentication token.</p>
                    </div>
                `;
                return;
            }
            
            results.innerHTML = '<p>Testing protected endpoints...</p>';
            
            try {
                // Test user info
                const userResponse = await fetch('/api/admin/user', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Accept': 'application/json'
                    }
                });
                const userData = await userResponse.json();
                
                // Test service requests
                const requestsResponse = await fetch('/api/service-requests', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Accept': 'application/json'
                    }
                });
                const requestsData = await requestsResponse.json();
                
                results.innerHTML = `
                    <div class="success">
                        <h4>✅ Protected Endpoints Working!</h4>
                        <p><strong>User Info:</strong> ${userData.user?.name || 'N/A'}</p>
                        <p><strong>Service Requests:</strong> ${requestsData.data?.length || 0} found</p>
                        <pre>${JSON.stringify(userData, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                results.innerHTML = `
                    <div class="error">
                        <h4>❌ Protected Endpoints Failed</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
