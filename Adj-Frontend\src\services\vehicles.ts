// ADJ Automotive Repair Services - Vehicles Service
// Handles vehicle inventory management and car sales

import { apiClient, PaginatedResponse, ApiResponse } from './api';

// Types
export interface VehicleImage {
  id: number;
  vehicle_id: number;
  image_url: string;
  image_path: string;
  alt_text?: string;
  is_primary: boolean;
  display_order: number;
  file_size?: number;
  mime_type?: string;
}

export interface Vehicle {
  id: number;
  title: string;
  make: string;
  model: string;
  year: number;
  price: string;
  mileage?: string;
  engine?: string;
  transmission?: string;
  drivetrain?: string;
  fuel_type?: string;
  exterior_color?: string;
  interior_color?: string;
  vin?: string;
  stock_number?: string;
  description?: string;
  features?: string[];
  status: 'available' | 'pending_sale' | 'sold' | 'maintenance';
  category?: 'sedan' | 'suv' | 'truck' | 'sports' | 'luxury' | 'compact' | 'convertible';
  featured: boolean;
  views: number;
  condition: 'new' | 'used' | 'certified';
  images: VehicleImage[];
  primary_image?: VehicleImage;
  formatted_price?: string;
  created_at: string;
  updated_at: string;
}

export interface VehicleFilters {
  status?: string;
  category?: string;
  featured?: boolean;
  search?: string;
  min_price?: number;
  max_price?: number;
  min_year?: number;
  max_year?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  per_page?: number;
  page?: number;
}

export interface CreateVehicleData {
  title: string;
  make: string;
  model: string;
  year: number;
  price: number;
  mileage?: string;
  engine?: string;
  transmission?: string;
  drivetrain?: string;
  fuel_type?: string;
  exterior_color?: string;
  interior_color?: string;
  vin?: string;
  stock_number?: string;
  description?: string;
  features?: string[];
  category?: string;
  featured?: boolean;
  condition: string;
}

export interface UpdateVehicleData extends Partial<CreateVehicleData> {
  status?: string;
}

// Vehicles Service Class
class VehiclesService {
  /**
   * Get vehicles list with filtering and pagination
   */
  async getVehicles(filters: VehicleFilters = {}): Promise<PaginatedResponse<Vehicle>> {
    const params = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value.toString());
      }
    });

    return apiClient.get<PaginatedResponse<Vehicle>>('/vehicles', Object.fromEntries(params));
  }

  /**
   * Get single vehicle by ID
   */
  async getVehicle(id: number): Promise<Vehicle> {
    const response = await apiClient.get<{ data: Vehicle }>(`/vehicles/${id}`);
    return response.data;
  }

  /**
   * Create new vehicle (admin only)
   */
  async createVehicle(vehicleData: CreateVehicleData): Promise<Vehicle> {
    const response = await apiClient.post<{ data: Vehicle; message: string }>('/vehicles', vehicleData);
    return response.data;
  }

  /**
   * Update vehicle (admin only)
   */
  async updateVehicle(id: number, vehicleData: UpdateVehicleData): Promise<Vehicle> {
    const response = await apiClient.put<{ data: Vehicle; message: string }>(`/vehicles/${id}`, vehicleData);
    return response.data;
  }

  /**
   * Delete vehicle (admin only)
   */
  async deleteVehicle(id: number): Promise<void> {
    await apiClient.delete(`/vehicles/${id}`);
  }

  /**
   * Upload images for vehicle (admin only)
   */
  async uploadVehicleImages(
    vehicleId: number, 
    images: File[], 
    primaryImageIndex: number = 0
  ): Promise<VehicleImage[]> {
    const formData = new FormData();
    
    images.forEach((image, index) => {
      formData.append('images[]', image);
    });
    
    formData.append('primary_image_index', primaryImageIndex.toString());

    const response = await apiClient.uploadFile<{ data: VehicleImage[]; message: string }>(
      `/vehicles/${vehicleId}/images`,
      formData
    );
    
    return response.data;
  }

  /**
   * Delete vehicle image (admin only)
   */
  async deleteVehicleImage(vehicleId: number, imageId: number): Promise<void> {
    await apiClient.delete(`/vehicles/${vehicleId}/images/${imageId}`);
  }

  /**
   * Get featured vehicles for homepage
   */
  async getFeaturedVehicles(limit: number = 6): Promise<Vehicle[]> {
    const response = await this.getVehicles({
      featured: true,
      status: 'available',
      per_page: limit,
      sort_by: 'created_at',
      sort_order: 'desc'
    });
    
    return response.data;
  }

  /**
   * Search vehicles by make/model
   */
  async searchVehicles(query: string, filters: Omit<VehicleFilters, 'search'> = {}): Promise<PaginatedResponse<Vehicle>> {
    return this.getVehicles({
      ...filters,
      search: query
    });
  }

  /**
   * Get vehicles by category
   */
  async getVehiclesByCategory(category: string, filters: Omit<VehicleFilters, 'category'> = {}): Promise<PaginatedResponse<Vehicle>> {
    return this.getVehicles({
      ...filters,
      category
    });
  }

  /**
   * Get vehicle statistics (admin only)
   */
  async getVehicleStats(): Promise<{
    total: number;
    available: number;
    sold: number;
    pending_sale: number;
    featured: number;
  }> {
    // This would need to be implemented as a separate endpoint in Laravel
    // For now, we'll calculate from the vehicles list
    const allVehicles = await this.getVehicles({ status: 'all', per_page: 1000 });
    
    const stats = {
      total: allVehicles.total,
      available: 0,
      sold: 0,
      pending_sale: 0,
      featured: 0
    };

    allVehicles.data.forEach(vehicle => {
      if (vehicle.status === 'available') stats.available++;
      if (vehicle.status === 'sold') stats.sold++;
      if (vehicle.status === 'pending_sale') stats.pending_sale++;
      if (vehicle.featured) stats.featured++;
    });

    return stats;
  }
}

// Export singleton instance
export const vehiclesService = new VehiclesService();

// Utility functions
export const formatPrice = (price: string | number): string => {
  const numPrice = typeof price === 'string' ? parseFloat(price) : price;
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(numPrice);
};

export const getVehicleDisplayName = (vehicle: Vehicle): string => {
  return `${vehicle.year} ${vehicle.make} ${vehicle.model}`;
};

export const getVehiclePrimaryImage = (vehicle: Vehicle): string | null => {
  const primaryImage = vehicle.images?.find(img => img.is_primary) || vehicle.images?.[0];
  return primaryImage?.image_url || null;
};
