# ADJ Automotive Repair Services - Frontend Project Context

## Project Overview

ADJ Automotive Repair Services is a comprehensive automotive service business application built with React/TypeScript frontend and Laravel backend. This documentation provides complete context for AI agents to understand the project structure and facilitate Laravel backend integration.

## Business Context

**Company**: ADJ Automotive Repair Services  
**Type**: Veteran Owned Small Business (VOSB)  
**Location**: 125 Chalan Ayuyu Yigo, Guam 96929  
**Contact**: (*************, <EMAIL>  
**Specialties**: Transmission rebuilding, engine repair, advanced diagnostics  

### Business Hours
- Monday - Friday: 8:00 AM - 5:00 PM
- Saturday: 9:00 AM - 2:00 PM
- Sunday: Closed

## Technology Stack

### Frontend Architecture
- **Framework**: React 18.3.1 with TypeScript
- **Build Tool**: Vite 7.0.6
- **Routing**: React Router DOM 6.26.2
- **Styling**: TailwindCSS 3.4.17
- **Animations**: Framer Motion 11.5.4
- **Icons**: Lucide React 0.441.0
- **State Management**: React Hooks (useState, useEffect, custom hooks)

### Development Tools
- **Linting**: ESLint with TypeScript support
- **Type Checking**: TypeScript 5.5.4
- **Package Manager**: NPM
- **CSS Processing**: PostCSS with Autoprefixer

## Project Structure

```
Adj-Frontend/
├── public/
│   ├── images/
│   │   ├── banners/          # Hero backgrounds, CTA images
│   │   ├── cars/             # Car inventory images
│   │   ├── logos/            # Company branding
│   │   └── services/         # Service icons (SVG)
├── src/
│   ├── components/           # Reusable UI components
│   ├── pages/               # Route-specific pages
│   ├── admin/               # Admin panel components
│   ├── hooks/               # Custom React hooks
│   └── App.tsx              # Main application component
├── package.json             # Dependencies and scripts
├── tailwind.config.js       # TailwindCSS configuration
├── tsconfig.json           # TypeScript configuration
└── vite.config.ts          # Vite build configuration
```

## Core Components & Features

### 1. Public Website Components

#### Navigation (`components/Navbar.tsx`)
- Responsive navigation with mobile hamburger menu
- Services dropdown with anchor links
- Logo integration with SVG support
- Sticky header with scroll-based styling
- Book Service CTA button

#### Hero Section (`components/HeroSection.tsx`)
- Multi-slide carousel with auto-play functionality
- Background images: `/images/banners/hero-bg1.jpg`, `hero-bg2.jpg`, `hero-bg3.jpg`
- Responsive background positioning
- Call-to-action buttons with navigation integration
- Slide indicators and navigation controls

#### Footer (`components/Footer.tsx`)
- Company contact information
- Business hours display
- Service links and quick navigation
- Social media integration (Instagram)

### 2. Service Management

#### Service Form (`components/ServiceForm.tsx`)
- Multi-step form with validation
- Customer information collection
- Vehicle details capture
- Service type selection
- Description/problem reporting
- Success state with confirmation message

**Service Types Offered:**
- Transmission Rebuilding
- Engine Repair & Rebuilding
- Advanced Diagnostics
- Brake Service
- Electrical Service
- Heating & Air Conditioning
- Suspension & Steering
- Exhaust Service
- Key Programming
- General Repair
- Fleet Service

### 3. Vehicle Sales System

#### Cars for Sale (`pages/CarsForSale.tsx`)
- Vehicle inventory display with filtering
- Search functionality by make/model
- Category filtering (sedan, SUV, sports, luxury)
- Sorting options (price, year, date added)
- Featured vehicle highlighting
- Responsive card layout with hover effects

#### Car Detail Views
- Individual vehicle detail pages
- Image galleries
- Specifications display
- Pricing and contact information

### 4. Admin Panel System

#### Authentication (`hooks/useAuth.ts`)
- Simple localStorage-based authentication
- Login/logout functionality
- Protected route management

#### Admin Dashboard (`admin/Dashboard.tsx`)
- Service request overview
- Car inquiry tracking
- Revenue visualization
- Appointment scheduling
- System notifications
- Quick action buttons

#### Car Management (`admin/CarManagement.tsx`)
- Vehicle inventory management
- Add/edit vehicle forms
- Image upload capabilities
- Status management (Available, Pending Sale, Sold)
- Featured vehicle toggle
- Search and filtering
- Pagination support

#### Additional Admin Features
- Service request management
- Customer database
- Car inquiries handling
- Reports generation
- System settings

## API Integration Requirements

### Expected Laravel Backend Endpoints

#### Authentication
```
POST /api/admin/login
POST /api/admin/logout
GET  /api/admin/user
```

#### Service Requests
```
GET    /api/service-requests
POST   /api/service-requests
PUT    /api/service-requests/{id}
DELETE /api/service-requests/{id}
GET    /api/service-requests/{id}
```

#### Vehicle Management
```
GET    /api/vehicles
POST   /api/vehicles
PUT    /api/vehicles/{id}
DELETE /api/vehicles/{id}
GET    /api/vehicles/{id}
POST   /api/vehicles/{id}/images
DELETE /api/vehicles/{id}/images/{imageId}
```

#### Car Inquiries
```
GET    /api/car-inquiries
POST   /api/car-inquiries
PUT    /api/car-inquiries/{id}
DELETE /api/car-inquiries/{id}
```

#### Customers
```
GET    /api/customers
POST   /api/customers
PUT    /api/customers/{id}
DELETE /api/customers/{id}
```

### Data Models Expected

#### Service Request Model
```typescript
interface ServiceRequest {
  id: number;
  customer_name: string;
  email: string;
  phone?: string;
  service_type: string;
  vehicle_make?: string;
  vehicle_model?: string;
  vehicle_year?: string;
  description: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  created_at: string;
  updated_at: string;
  estimated_cost?: number;
  scheduled_date?: string;
}
```

#### Vehicle Model
```typescript
interface Vehicle {
  id: number;
  title: string;
  make: string;
  model: string;
  year: number;
  price: string;
  mileage: string;
  engine?: string;
  transmission?: string;
  exterior_color?: string;
  interior_color?: string;
  vin?: string;
  stock_number?: string;
  description?: string;
  status: 'available' | 'pending_sale' | 'sold';
  featured: boolean;
  images: VehicleImage[];
  views: number;
  created_at: string;
  updated_at: string;
}

interface VehicleImage {
  id: number;
  vehicle_id: number;
  image_url: string;
  is_primary: boolean;
  order: number;
}
```

#### Car Inquiry Model
```typescript
interface CarInquiry {
  id: number;
  vehicle_id: number;
  customer_name: string;
  email: string;
  phone?: string;
  message?: string;
  status: 'pending' | 'contacted' | 'scheduled' | 'sold' | 'cancelled';
  created_at: string;
  updated_at: string;
  vehicle?: Vehicle;
}
```

#### Customer Model
```typescript
interface Customer {
  id: number;
  name: string;
  email: string;
  phone?: string;
  address?: string;
  created_at: string;
  updated_at: string;
  service_requests?: ServiceRequest[];
  car_inquiries?: CarInquiry[];
}
```

## Key Integration Points

### 1. Form Submissions
- **Service Form**: Needs to POST to `/api/service-requests`
- **Car Inquiry**: Needs to POST to `/api/car-inquiries`
- **Contact Forms**: Needs to POST to `/api/contacts`

### 2. Admin Panel Data
- Dashboard statistics and recent activity
- Service request management with status updates
- Vehicle inventory management with image uploads
- Customer database with search functionality

### 3. Public Data Display
- Vehicle listings for cars page
- Featured vehicles for homepage
- Service pricing information
- Company information and settings

## Environment Configuration

### Required Environment Variables
```env
VITE_API_BASE_URL=http://localhost:8000/api
VITE_APP_NAME=ADJ Automotive Repair Services
VITE_COMPANY_PHONE=(*************
VITE_COMPANY_EMAIL=<EMAIL>
VITE_COMPANY_ADDRESS=125 Chalan Ayuyu Yigo, Guam 96929
```

### Image Storage Considerations
- Vehicle images need to be stored and served by Laravel
- Service icons are stored locally in `/public/images/services/`
- Company logos and banners are stored locally in `/public/images/`
- Consider implementing image optimization and CDN integration

## Responsive Design & UX

### Breakpoints (TailwindCSS)
- Mobile: < 640px
- Tablet: 640px - 1024px
- Desktop: > 1024px

### Key UX Features
- Smooth animations with Framer Motion
- Loading states for form submissions
- Error handling and validation
- Mobile-first responsive design
- Accessibility considerations with proper ARIA labels

## Development Workflow

### Available Scripts
```bash
npm run dev      # Start development server
npm run build    # Build for production
npm run preview  # Preview production build
npm run lint     # Run ESLint
```

### Code Organization Patterns
- Component-based architecture
- Custom hooks for state management
- TypeScript interfaces for type safety
- Consistent naming conventions
- Responsive utility classes

## Security Considerations

### Frontend Security
- Input validation on all forms
- XSS prevention with React's built-in escaping
- CSRF token handling for API requests
- Secure authentication token storage
- Environment variable protection

### Backend Integration Requirements
- CORS configuration for frontend domain
- API rate limiting
- Authentication middleware
- Input validation and sanitization
- File upload security for vehicle images

## Performance Optimization

### Current Optimizations
- Code splitting with React.lazy (potential)
- Image optimization with proper formats
- Bundle size optimization with Vite
- CSS purging with TailwindCSS

### Recommended Laravel Backend Optimizations
- API response caching
- Image resizing and compression
- Database query optimization
- Pagination for large datasets
- CDN integration for static assets

## Testing Strategy

### Frontend Testing Needs
- Component unit tests
- Integration tests for forms
- E2E tests for critical user flows
- Accessibility testing

### API Testing Requirements
- Endpoint functionality testing
- Authentication flow testing
- Data validation testing
- Error handling verification

## Deployment Considerations

### Frontend Deployment
- Static site deployment (Netlify, Vercel)
- Environment variable configuration
- Build optimization
- CDN integration

### Backend Integration
- API endpoint configuration
- Database connection setup
- File storage configuration
- SSL certificate requirements

---

This documentation provides comprehensive context for AI agents to understand the ADJ Automotive Repair Services frontend application and effectively integrate it with a Laravel backend API. The project follows modern React development practices and is ready for backend integration with the specified data models and API endpoints.
