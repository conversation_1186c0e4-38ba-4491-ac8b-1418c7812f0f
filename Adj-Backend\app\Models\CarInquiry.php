<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CarInquiry extends Model
{
    use HasFactory;

    protected $fillable = [
        'vehicle_id',
        'customer_id',
        'customer_name',
        'email',
        'phone',
        'message',
        'inquiry_type',
        'status',
        'contacted_at',
        'scheduled_date',
        'notes',
    ];

    protected $casts = [
        'contacted_at' => 'datetime',
        'scheduled_date' => 'datetime',
    ];

    // Relationships
    public function vehicle()
    {
        return $this->belongsTo(Vehicle::class);
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    // Scopes
    public function scopeStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeInquiryType($query, $type)
    {
        return $query->where('inquiry_type', $type);
    }
}
