<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\RegisterController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// API Status Route
Route::get('/', function () {
    return response()->json([
        'message' => 'ADJ Automotive Repair Services API',
        'version' => '1.0.0',
        'status' => 'active',
        'frontend_url' => 'http://localhost:3000',
        'admin_login' => 'http://localhost:3000/admin/login'
    ]);
});

// Registration Routes (for testing only)
Route::prefix('admin')->name('admin.')->group(function () {
    Route::get('/register', [RegisterController::class, 'showRegistrationForm'])->name('register');
    Route::post('/register', [RegisterController::class, 'register']);
});
