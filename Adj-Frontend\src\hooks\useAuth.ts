import { useState, useEffect } from 'react';
import { authService, User, LoginCredentials } from '../services/auth';
import { ApiError, isApiError } from '../services/api';

export interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  error: string | null;
}

export const useAuth = (): AuthState & {
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  clearError: () => void;
} => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<User | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Initialize auth state on app load
    const initializeAuth = async () => {
      setIsLoading(true);
      try {
        const currentUser = await authService.initialize();
        if (currentUser) {
          setIsAuthenticated(true);
          setUser(currentUser);
        } else {
          setIsAuthenticated(false);
          setUser(null);
        }
      } catch (err) {
        console.error('Auth initialization failed:', err);
        setIsAuthenticated(false);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (credentials: LoginCredentials): Promise<void> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await authService.login(credentials);
      setIsAuthenticated(true);
      setUser(response.user);
    } catch (err) {
      setIsAuthenticated(false);
      setUser(null);

      if (isApiError(err)) {
        setError(err.message);
      } else {
        setError('Login failed. Please try again.');
      }
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    setIsLoading(true);
    setError(null);

    try {
      await authService.logout();
    } catch (err) {
      console.error('Logout error:', err);
      // Don't throw error for logout - we want to clear state regardless
    } finally {
      setIsAuthenticated(false);
      setUser(null);
      setIsLoading(false);
    }
  };

  const refreshUser = async (): Promise<void> => {
    if (!isAuthenticated) return;

    try {
      const currentUser = await authService.refreshUser();
      if (currentUser) {
        setUser(currentUser);
      } else {
        // User session expired
        setIsAuthenticated(false);
        setUser(null);
      }
    } catch (err) {
      console.error('User refresh failed:', err);
      setIsAuthenticated(false);
      setUser(null);
    }
  };

  const clearError = (): void => {
    setError(null);
  };

  return {
    isAuthenticated,
    isLoading,
    user,
    error,
    login,
    logout,
    refreshUser,
    clearError
  };
};