import { useEffect, useRef, useState } from 'react';
import { motion, useAnimation, useInView } from 'framer-motion';
import { Link } from 'react-router-dom';
import HeroSection from '../components/HeroSection';
import { StarIcon } from 'lucide-react';
import { apiClient } from '../services/api';
const Home = () => {
  return <main className="w-full">
      <HeroSection />
      <FeaturedServices />
      <WhyChooseUs />
      <FeaturedCars />
      <TestimonialsSection />
      <CtaSection />
    </main>;
};
const FeaturedServices = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: true,
    margin: '-100px'
  });
  const controls = useAnimation();
  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);
  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 50,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };
  const services = [{
    title: 'Transmission Rebuilding',
    description: 'Make your old transmission like brand new with our expert rebuilding service, backed by a 1-year labor warranty.',
    image: 'https://images.unsplash.com/photo-1619642751034-765dfdf7c58e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1335&q=80',
    link: '/services#transmission'
  }, {
    title: 'Engine Repair',
    description: 'Complete engine overhauls and professional-grade service to keep your vehicle running at peak performance.',
    image: 'https://images.unsplash.com/photo-1486262715619-67b85e0b08d3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1335&q=80',
    link: '/services#engine'
  }, {
    title: 'Advanced Diagnostics',
    description: 'Using the latest Autel MaxiSys Ultra scan tool to diagnose and resolve even the most complex vehicle issues.',
    image: 'https://images.unsplash.com/photo-1601362840469-51e4d8d58785?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1335&q=80',
    link: '/services#diagnostics'
  }];
  return <section className="py-20 relative overflow-hidden">
      {/* Background Image with enhanced styling */}
      <div className="absolute inset-0 bg-cover bg-center bg-no-repeat" style={{
          backgroundImage: 'url("/images/banners/background2.jpg")'
        }}>
        {/* Multi-layered overlay for depth and readability */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/85 via-blue-50/90 to-slate-100/85"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-slate-200/60 via-transparent to-white/40"></div>
      </div>
      <div className="container mx-auto px-4 md:px-8 relative z-10">
        <motion.div ref={ref} variants={containerVariants} initial="hidden" animate={controls} className="text-center mb-16">
          <motion.span variants={itemVariants} className="inline-block bg-blue-600/10 backdrop-blur-sm text-blue-800 px-4 py-2 rounded-full text-sm font-semibold mb-3 border border-blue-200/50">
            OUR SPECIALTIES
          </motion.span>
          <motion.h2 variants={itemVariants} className="text-4xl font-bold text-gray-900 mb-4 drop-shadow-sm">
            Expert Automotive Services
          </motion.h2>
          <motion.p variants={itemVariants} className="text-xl text-gray-700 max-w-3xl mx-auto drop-shadow-sm">
            We provide dealership quality repairs at affordable prices, with a
            focus on transmission and engine work.
          </motion.p>
        </motion.div>
        <motion.div className="grid grid-cols-1 md:grid-cols-3 gap-8" variants={containerVariants} initial="hidden" animate={controls}>
          {services.map((service, index) => <motion.div key={index} initial={{
          y: 50,
          opacity: 0
        }} animate={{
          y: 0,
          opacity: 1,
          transition: {
            duration: 0.6,
            delay: index * 0.1,
            ease: 'easeOut'
          }
        }} whileHover={{
          y: -10,
          transition: {
            type: "spring",
            stiffness: 400,
            damping: 25
          }
        }} style={{
          transform: 'translateY(0px)'
        }} className="bg-white/95 backdrop-blur-sm rounded-2xl overflow-hidden shadow-xl border border-white/50 hover:shadow-2xl transition-all duration-300">
              <div className="h-56 overflow-hidden">
                <img src={service.image} alt={service.title} className="w-full h-full object-cover transition-transform duration-500 hover:scale-110" />
              </div>
              <div className="p-6">
                <h3 className="text-2xl font-bold text-gray-900 mb-3">
                  {service.title}
                </h3>
                <p className="text-gray-600 mb-6">{service.description}</p>
                <Link to={service.link} className="inline-block bg-[#1e3a5f] hover:bg-blue-800 text-white font-semibold py-2 px-6 rounded-lg transition-all duration-300 hover:scale-105 active:scale-95">
                  Learn More
                </Link>
              </div>
            </motion.div>)}
        </motion.div>
        <motion.div variants={itemVariants} className="text-center mt-12" initial="hidden" animate={controls}>
          <Link to="/services" className="inline-block border-2 border-[#1e3a5f] bg-white/80 backdrop-blur-sm text-[#1e3a5f] hover:bg-[#1e3a5f] hover:text-white font-semibold py-3 px-8 rounded-full transition-all duration-300 hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl">
            View All Services
          </Link>
        </motion.div>
      </div>
    </section>;
};
const WhyChooseUs = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: true,
    margin: '-100px'
  });
  const controls = useAnimation();
  const [activeHotspot, setActiveHotspot] = useState<number | null>(null);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Function to get smart tooltip positioning for mobile
  const getTooltipPosition = (hotspot: any) => {
    if (!isMobile) {
      return {
        top: '50%',
        left: (hotspot.left.includes('77') || hotspot.left.includes('57')) ? 'auto' : 'calc(100% + 16px)',
        right: (hotspot.left.includes('77') || hotspot.left.includes('57')) ? 'calc(100% + 16px)' : 'auto',
        transform: 'translateY(-50%)',
      };
    }

    // Mobile positioning with edge detection
    const leftPercentage = parseFloat(hotspot.left.replace('%', ''));
    
    if (leftPercentage > 55) {
      // Right side hotspots - ensure tooltip stays within screen bounds
      return {
        top: 'calc(100% + 8px)',
        left: 'auto',
        right: '0.5rem', // Safe margin from right edge
        transform: 'none',
      };
    } else if (leftPercentage < 30) {
      // Left side hotspots - align tooltip to the left with margin
      return {
        top: 'calc(100% + 8px)',
        left: '-8px',
        right: 'auto',
        transform: 'none',
      };
    } else {
      // Center hotspots (30-55%) - center the tooltip
      return {
        top: 'calc(100% + 8px)',
        left: '50%',
        right: 'auto',
        transform: 'translateX(-50%)',
      };
    }
  };

  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);

  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: {
      y: 30,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: 'easeOut'
      }
    }
  };

  const hotspots = [
    {
      id: 1,
      top: '40%',
      left: '32%',
      icon: '/images/services/car-diagnostic.svg',
      title: 'Diagnostics',
      description: 'If your car needs a mobile diagnostic check done at your home or office, let AutoMechanica come to you.'
    },
    {
      id: 2,
      top: '42%',
      left: '12%',
      icon: '/images/services/car-paint.svg',
      title: 'Dent & Paint',
      description: 'AutoMechanica specializes in car dent repair and car painting services for a range of models.'
    },
    {
      id: 3,
      top: '31%',
      left: '20%',
      icon: '/images/services/car-lube.svg',
      title: 'Oil / Lube / Filters',
      description: 'AutoMechanica proudly serves the lube, oil, and filter change needs of your vehicle, which, if done at regular intervals, extends the life of your car.'
    },
    {
      id: 4,
      top: '72%',
      left: '77.5%',
      icon: '/images/services/car-brake.svg',
      title: 'Brakes',
      description: 'The brake system consists of different parts that can be fixed individually. A detailed quote is given to you after we perform our systematic brake evaluation.'
    },
    {
      id: 5,
      top: '45%',
      left: '77.5%',
      icon: '/images/services/car-suspension.svg',
      title: 'Suspension',
      description: 'The suspension system of your vehicle protects you from bouncing up and down due to the bad road conditions and bumps in the road.'
    },
    {
      id: 6,
      top: '43%',
      left: '57.5%',
      icon: '/images/services/car-detailing.svg',
      title: 'Detailing',
      description: 'AutoMechanica offers professional car detail services at an affordable price. Our interior cleaning, detailing, and restoration services can help you recapture that new car look and smell.'
    }
  ];

  const services = [{
    icon: '/images/services/car-diagnostic.svg',
    title: 'Diagnostics',
    description: 'If your car needs a mobile diagnostic check done at your home or office, let AutoMechanica come to you.',
    link: '/periodic-maintenance-service'
  }, {
    icon: '/images/services/car-paint.svg',
    title: 'Dent & Paint',
    description: 'AutoMechanica specializes in car dent repair and car painting services for a range of models.',
    link: '/dent-paint'
  }, {
    icon: '/images/services/car-lube.svg',
    title: 'Oil / Lube / Filters',
    description: 'AutoMechanica proudly serves the Lube, Oil & Filter change needs of customers\' vehicle performance while extending the life of your vehicle.',
    link: '/transmission-and-clutch-repairs'
  }, {
    icon: '/images/services/car-brake.svg',
    title: 'Brakes',
    description: 'The brake system consists of different parts that can be fixed individually. A detailed quote is given to you after we perform our systematic brake evaluation.',
    link: '/break-repairs'
  }, {
    icon: '/images/services/car-suspension.svg',
    title: 'Suspension',
    description: 'The suspension system of your vehicle protects you from bouncing up and down due to the bad road conditions and bumps in the road.',
    link: '/suspension-repairs'
  }, {
    icon: '/images/services/car-detailing.svg',
    title: 'Detailing',
    description: 'AutoMechanica offers professional car detail services at an affordable price. Our interior cleaning, detailing, and restoration services can help you recapture that new car look and smell.',
    link: '/9h-ceramic-coating'
  }];

  return <section className="py-20 bg-gradient-to-b from-[#1e3a5f] to-[#0f2542]">
      <div className="container mx-auto px-4 md:px-8">
        <motion.div ref={ref} variants={containerVariants} initial="hidden" animate={controls} className="text-center mb-16">
          <motion.span variants={itemVariants} className="inline-block bg-blue-900 text-blue-100 px-4 py-1 rounded-full text-sm font-semibold mb-3">
            WHY CHOOSE US
          </motion.span>
          <motion.h2 variants={itemVariants} className="text-4xl font-bold text-white mb-4">
            We Offer Full Service Auto Repair & Maintenance
          </motion.h2>
          <motion.p variants={itemVariants} className="text-xl text-blue-100 max-w-3xl mx-auto">
            Explore our comprehensive automotive services by {isMobile ? 'tapping' : 'hovering over'} the hotspots below
          </motion.p>
        </motion.div>

        {/* Interactive Car Section */}
        <motion.div variants={itemVariants} className="relative mb-16 max-w-4xl mx-auto overflow-visible">
          {/* Mobile overlay to close tooltips */}
          {isMobile && activeHotspot && (
            <div 
              className="fixed inset-0 z-30 bg-black/20"
              onClick={() => setActiveHotspot(null)}
            />
          )}
          <div className="relative rounded-3xl p-4 md:p-8">
            {/* Seamless organic glow effect */}
            <div className="absolute inset-0 pointer-events-none overflow-hidden rounded-3xl">
              {/* Multiple scattered glow points for organic effect */}
              <div 
                className="absolute blur-3xl opacity-60"
                style={{
                  top: '10%',
                  left: '20%',
                  width: '60%',
                  height: '80%',
                  background: 'radial-gradient(ellipse 120% 100% at center, rgba(96, 165, 250, 0.2) 0%, rgba(59, 130, 246, 0.1) 30%, transparent 60%)',
                  borderRadius: '50%'
                }}
              ></div>
              <div 
                className="absolute blur-2xl opacity-70"
                style={{
                  top: '5%',
                  left: '15%',
                  width: '70%',
                  height: '90%',
                  background: 'radial-gradient(ellipse 110% 90% at center, rgba(59, 130, 246, 0.15) 0%, rgba(37, 99, 235, 0.08) 40%, transparent 70%)',
                  borderRadius: '50%'
                }}
              ></div>
              <div 
                className="absolute blur-xl opacity-80"
                style={{
                  top: '15%',
                  left: '25%',
                  width: '50%',
                  height: '70%',
                  background: 'radial-gradient(ellipse 100% 80% at center, rgba(37, 99, 235, 0.18) 0%, rgba(59, 130, 246, 0.12) 50%, transparent 80%)',
                  borderRadius: '50%'
                }}
              ></div>
              {/* Ambient background glow */}
              <div 
                className="absolute inset-0 blur-2xl opacity-40"
                style={{
                  background: 'conic-gradient(from 0deg at 50% 50%, transparent 0deg, rgba(96, 165, 250, 0.05) 90deg, rgba(59, 130, 246, 0.08) 180deg, rgba(37, 99, 235, 0.05) 270deg, transparent 360deg)',
                  borderRadius: '50%'
                }}
              ></div>
            </div>
            <div className="relative">
              <img 
                src="/images/banners/carbg.png" 
                alt="Car Service Areas" 
                className="w-full h-auto"
                style={{
                  filter: 'drop-shadow(0 20px 40px rgba(59, 130, 246, 0.4)) drop-shadow(0 8px 16px rgba(37, 99, 235, 0.3))'
                }}
                onError={(e) => {
                  // Fallback to a different car image if the local one doesn't work
                  e.currentTarget.src = 'https://images.unsplash.com/photo-1580273916550-e323be2ae537?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1464&q=80';
                }}
              />
              
              {/* Hotspots */}
              {hotspots.map((hotspot) => (
                <div
                  key={hotspot.id}
                  className="absolute"
                  style={{
                    top: hotspot.top,
                    left: hotspot.left,
                    transform: 'translate(-50%, -50%)', // Center the hotspot
                    zIndex: activeHotspot === hotspot.id ? 50 : 40 // Elevate active hotspot
                  }}
                  onMouseEnter={() => !isMobile && setActiveHotspot(hotspot.id)}
                  onMouseLeave={() => !isMobile && setActiveHotspot(null)}
                  onClick={() => isMobile && setActiveHotspot(activeHotspot === hotspot.id ? null : hotspot.id)}
                >
                  <motion.div
                    className="relative cursor-pointer"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {/* Hotspot Dot */}
                    <div className={`${isMobile ? 'w-6 h-6' : 'w-8 h-8'} bg-red-600 rounded-full flex items-center justify-center shadow-lg border-2 border-white transition-all duration-200`}>
                      <motion.div
                        className={`${isMobile ? 'w-2 h-2' : 'w-3 h-3'} bg-white rounded-full`}
                        animate={{
                          scale: [1, 1.2, 1],
                          opacity: [1, 0.8, 1]
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      />
                    </div>

                    {/* Tooltip */}
                    {activeHotspot === hotspot.id && (
                      <motion.div
                        initial={{ opacity: 0, y: 10, scale: 0.9 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        exit={{ opacity: 0, y: 10, scale: 0.9 }}
                        className={`absolute z-50 ${isMobile ? 'pointer-events-auto' : 'pointer-events-none'}`}
                        style={getTooltipPosition(hotspot)}
                      >
                        <div className={`bg-white rounded-lg shadow-xl relative ${
                          isMobile ? 'w-52 max-w-[calc(100vw-1.5rem)]' : 'w-60'
                        }`}>
                          <div className={`${isMobile ? 'p-3' : 'p-4'} relative z-10`}>
                            <div className={`flex items-center ${isMobile ? 'mb-1' : 'mb-2'}`}>
                              <div className={`text-blue-600 mr-2 flex-shrink-0 ${isMobile ? 'scale-90' : ''}`}>
                                <img 
                                  src={hotspot.icon} 
                                  alt={hotspot.title} 
                                  className="h-5 w-5"
                                />
                              </div>
                              <h6 className={`font-bold text-gray-900 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                                {hotspot.title}
                              </h6>
                            </div>
                            <p className={`text-gray-600 leading-relaxed ${isMobile ? 'text-xs' : 'text-sm'}`}>
                              {hotspot.description}
                            </p>
                            {isMobile && (
                              <button 
                                className="mt-2 text-blue-600 text-xs font-medium"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setActiveHotspot(null);
                                }}
                              >
                                Close
                              </button>
                            )}
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </motion.div>
                </div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Service Grid - Updated to horizontal row layout */}
        <motion.div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6" variants={containerVariants} initial="hidden" animate={controls}>
          {services.map((service, index) => <motion.div 
            key={index} 
            initial={{
              y: 30,
              opacity: 0
            }} 
            animate={{
              y: 0,
              opacity: 1,
              transition: {
                duration: 0.5,
                delay: index * 0.1,
                ease: 'easeOut'
              }
            }} 
            whileHover={{
              y: -5,
              backgroundColor: '#ffffff',
              transition: {
                type: "spring",
                stiffness: 400,
                damping: 25
              }
            }} 
            style={{
              transform: 'translateY(0px)'
            }} 
            className="bg-white/95 rounded-xl p-6 shadow-xl transition-all hover:shadow-2xl"
          >
              <div className="mb-4 flex justify-center">
                <img 
                  src={service.icon} 
                  alt={service.title} 
                  className="h-10 w-10"
                />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2 text-center">
                {service.title}
              </h3>
              <p className="text-gray-600 text-center text-sm leading-relaxed">
                {service.description}
              </p>
            </motion.div>)}
        </motion.div>
      </div>
    </section>;
};
const FeaturedCars = () => {
  const [cars, setCars] = useState([]);
  const [loading, setLoading] = useState(true);
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: true,
    margin: '-100px'
  });

  // Fetch featured vehicles from API
  useEffect(() => {
    const fetchFeaturedVehicles = async () => {
      try {
        setLoading(true);
        const response = await apiClient.get('/vehicles?featured=1');
        setCars(response.data?.slice(0, 3) || []); // Show only first 3 featured cars
      } catch (err) {
        console.error('Error fetching featured vehicles:', err);
        setCars([]); // Show empty state on error
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedVehicles();
  }, []);
  const controls = useAnimation();
  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);
  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 30,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };

  return <section className="py-20 relative overflow-hidden">
      {/* Background Image with enhanced styling */}
      <div className="absolute inset-0 bg-cover bg-center bg-no-repeat" style={{
        backgroundImage: 'url("/images/banners/background3.jpg")'
      }}>
        {/* Multi-layered overlay for depth and readability */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-50/90 via-gray-100/85 to-blue-50/90"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-gray-200/50 via-transparent to-slate-100/40"></div>
      </div>
      <div className="container mx-auto px-4 md:px-8 relative z-10">
        <motion.div ref={ref} variants={containerVariants} initial="hidden" animate={controls} className="text-center mb-16">
          <motion.span variants={itemVariants} className="inline-block bg-blue-600/10 backdrop-blur-sm text-blue-800 px-4 py-2 rounded-full text-sm font-semibold mb-3 border border-blue-200/50">
            FEATURED VEHICLES
          </motion.span>
          <motion.h2 variants={itemVariants} className="text-4xl font-bold text-gray-900 mb-4 drop-shadow-sm">
            Quality Cars for Sale
          </motion.h2>
          <motion.p variants={itemVariants} className="text-xl text-gray-700 max-w-3xl mx-auto drop-shadow-sm">
            Browse our selection of quality pre-owned vehicles at competitive
            prices.
          </motion.p>
        </motion.div>
        <motion.div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" variants={containerVariants} initial="hidden" animate={controls}>
          {loading ? (
            // Loading skeleton
            Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="bg-white rounded-2xl overflow-hidden shadow-xl animate-pulse">
                <div className="h-56 bg-gray-300"></div>
                <div className="p-6">
                  <div className="h-6 bg-gray-300 rounded mb-3"></div>
                  <div className="h-4 bg-gray-300 rounded mb-2"></div>
                  <div className="h-4 bg-gray-300 rounded w-2/3"></div>
                </div>
              </div>
            ))
          ) : cars.length > 0 ? (
            cars.map((car, index) => <motion.div key={car.id} initial={{
          y: 30,
          opacity: 0
        }} animate={{
          y: 0,
          opacity: 1,
          transition: {
            duration: 0.6,
            delay: index * 0.1,
            ease: 'easeOut'
          }
        }} whileHover={{
          y: -10,
          transition: {
            type: "spring",
            stiffness: 400,
            damping: 25
          }
        }} style={{
          transform: 'translateY(0px)'
        }} className="bg-white/95 backdrop-blur-sm rounded-2xl overflow-hidden shadow-xl border border-white/50 hover:shadow-2xl transition-all duration-300 relative">
              {car.featured && <div className="absolute top-4 left-4 z-10">
                  <span className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white text-sm font-bold px-3 py-1 rounded-full shadow-lg">
                    Featured
                  </span>
                </div>}
              <div className="h-56 overflow-hidden">
                <img
                  src={car.primary_image?.url || car.images?.[0]?.url || 'https://via.placeholder.com/400x300?text=No+Image'}
                  alt={car.title}
                  className="w-full h-full object-cover transition-transform duration-500 hover:scale-110"
                />
              </div>
              <div className="p-6">
                <div className="flex justify-between items-center mb-3">
                  <h3 className="text-xl font-bold text-gray-900">
                    {car.title}
                  </h3>
                  <span className="text-xl font-bold text-blue-700">
                    ${parseFloat(car.price).toLocaleString()}
                  </span>
                </div>
                <p className="text-gray-600 mb-6">Mileage: {car.mileage || 'N/A'}</p>
                <Link to={`/cars/${car.id}`} className="inline-block bg-[#1e3a5f] hover:bg-blue-800 text-white font-semibold py-2 px-6 rounded-lg transition-all duration-300 hover:scale-105 active:scale-95 w-full text-center">
                  View Details
                </Link>
              </div>
            </motion.div>)
          ) : (
            <div className="col-span-full text-center py-12">
              <p className="text-gray-600 text-lg">No featured vehicles available at the moment.</p>
              <Link to="/cars" className="inline-block mt-4 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                View All Cars
              </Link>
            </div>
          )}
        </motion.div>
        <motion.div variants={itemVariants} className="text-center mt-12" initial="hidden" animate={controls}>
          <Link to="/cars" className="inline-block border-2 border-[#1e3a5f] bg-white/80 backdrop-blur-sm text-[#1e3a5f] hover:bg-[#1e3a5f] hover:text-white font-semibold py-3 px-8 rounded-full transition-all duration-300 hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl">
            View All Cars
          </Link>
        </motion.div>
      </div>
    </section>;
};
const TestimonialsSection = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: true,
    margin: '-100px'
  });
  const controls = useAnimation();
  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);
  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 30,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };
  const testimonials = [{
    name: 'Michael Rodriguez',
    role: 'Toyota Owner',
    image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=387&q=80',
    text: 'ADJ Automotive rebuilt the transmission in my Toyota and it runs better than new. Their attention to detail and expertise is unmatched. Highly recommend their services!'
  }, {
    name: 'Sarah Johnson',
    role: 'Ford F-150 Owner',
    image: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=387&q=80',
    text: "I brought my F-150 in with engine issues that two other shops couldn't diagnose. ADJ found and fixed the problem in one day. Their diagnostic equipment is top-notch!"
  }, {
    name: 'David Chen',
    role: 'Mercedes Owner',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=387&q=80',
    text: "As a Mercedes owner, I was worried about finding quality service outside the dealership. ADJ's master certified technicians provided exceptional service at half the dealership price."
  }];
  return <section className="py-20 bg-gradient-to-b from-[#0f2542] to-[#1e3a5f]">
      <div className="container mx-auto px-4 md:px-8">
        <motion.div ref={ref} variants={containerVariants} initial="hidden" animate={controls} className="text-center mb-16">
          <motion.span variants={itemVariants} className="inline-block bg-blue-900 text-blue-100 px-4 py-1 rounded-full text-sm font-semibold mb-3">
            TESTIMONIALS
          </motion.span>
          <motion.h2 variants={itemVariants} className="text-4xl font-bold text-white mb-4">
            What Our Customers Say
          </motion.h2>
          <motion.p variants={itemVariants} className="text-xl text-blue-100 max-w-3xl mx-auto">
            Don't just take our word for it. Here's what our satisfied customers
            have to say.
          </motion.p>
        </motion.div>
        <motion.div className="grid grid-cols-1 md:grid-cols-3 gap-8" variants={containerVariants} initial="hidden" animate={controls}>
          {testimonials.map((testimonial, index) => <motion.div key={index} initial={{
          y: 30,
          opacity: 0
        }} animate={{
          y: 0,
          opacity: 1,
          transition: {
            duration: 0.6,
            delay: index * 0.1,
            ease: 'easeOut'
          }
        }} whileHover={{
          y: -10,
          transition: {
            type: "spring",
            stiffness: 400,
            damping: 25
          }
        }} style={{
          transform: 'translateY(0px)'
        }} className="bg-white rounded-2xl p-8 shadow-xl">
              <div className="flex items-center mb-6">
                <div className="h-16 w-16 rounded-full overflow-hidden mr-4">
                  <img src={testimonial.image} alt={testimonial.name} className="w-full h-full object-cover" />
                </div>
                <div>
                  <h4 className="text-lg font-bold text-gray-900">
                    {testimonial.name}
                  </h4>
                  <p className="text-blue-600">{testimonial.role}</p>
                </div>
              </div>
              <p className="text-gray-600 italic">"{testimonial.text}"</p>
              <div className="mt-4 flex">
                {[...Array(5)].map((_, i) => <StarIcon key={i} className="h-5 w-5 text-yellow-500" />)}
              </div>
            </motion.div>)}
        </motion.div>
      </div>
    </section>;
};
const CtaSection = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: true,
    margin: '-100px'
  });
  const controls = useAnimation();
  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);
  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 30,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };
  return <section className="py-20 bg-slate-100 relative overflow-hidden">
      <div className="absolute inset-0 z-0 opacity-10">
        <div className="w-full h-full bg-cover bg-center" style={{
        backgroundImage: "url('https://images.unsplash.com/photo-1486262715619-67b85e0b08d3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1335&q=80')"
      }}></div>
      </div>
      <div className="container mx-auto px-4 md:px-8 relative z-10">
        <motion.div ref={ref} variants={containerVariants} initial="hidden" animate={controls} className="bg-[#1e3a5f] rounded-3xl p-8 md:p-16 shadow-2xl">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            <div>
              <motion.span variants={itemVariants} className="inline-block bg-blue-800 text-blue-100 px-4 py-1 rounded-full text-sm font-semibold mb-3">
                GET STARTED TODAY
              </motion.span>
              <motion.h2 variants={itemVariants} className="text-3xl md:text-4xl font-bold text-white mb-4">
                Ready to experience dealership quality repair at an affordable
                price?
              </motion.h2>
              <motion.p variants={itemVariants} className="text-lg text-blue-100 mb-8">
                Whether you need transmission work, engine repair, or any
                automotive service, our team of certified experts is ready to
                help.
              </motion.p>
              <motion.div variants={itemVariants} className="flex flex-wrap gap-4">
                <Link to="/book-service">
                  <motion.button className="bg-white hover:bg-gray-100 text-[#1e3a5f] px-8 py-3 rounded-full font-semibold text-lg" whileHover={{
                  scale: 1.05
                }} whileTap={{
                  scale: 0.95
                }} transition={{
                  type: 'spring',
                  stiffness: 400,
                  damping: 17
                }}>
                    Book Appointment
                  </motion.button>
                </Link>
                <motion.a href="tel:+16714838335" className="border-2 border-white text-white hover:bg-white hover:text-[#1e3a5f] px-8 py-3 rounded-full font-semibold text-lg inline-flex items-center transition-colors" whileHover={{
                scale: 1.05
              }} whileTap={{
                scale: 0.95
              }} transition={{
                type: 'spring',
                stiffness: 400,
                damping: 17
              }}>
                  Call (*************
                </motion.a>
              </motion.div>
            </div>
            <motion.div variants={itemVariants} className="hidden md:block">
              <img
                src="/images/banners/cta-image.jpg"
                alt="Mechanic working on car"
                className="rounded-xl shadow-lg w-full h-80 object-cover"
                onError={(e) => {
                  e.currentTarget.src =
                    'https://images.unsplash.com/photo-1560179304-6fc1d8749b23?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80';
                }}
              />
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>;
};
export default Home;