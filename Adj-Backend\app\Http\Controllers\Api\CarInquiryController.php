<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\CarInquiry;
use App\Models\Customer;
use App\Models\Vehicle;
use Illuminate\Http\Request;

class CarInquiryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = CarInquiry::with(['vehicle', 'customer']);

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Filter by inquiry type
        if ($request->has('inquiry_type')) {
            $query->where('inquiry_type', $request->inquiry_type);
        }

        // Filter by vehicle
        if ($request->has('vehicle_id')) {
            $query->where('vehicle_id', $request->vehicle_id);
        }

        // Search functionality
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('customer_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhereHas('vehicle', function ($vehicleQuery) use ($search) {
                      $vehicleQuery->where('title', 'like', "%{$search}%")
                                  ->orWhere('make', 'like', "%{$search}%")
                                  ->orWhere('model', 'like', "%{$search}%");
                  });
            });
        }

        $query->orderBy('created_at', 'desc');
        $inquiries = $query->paginate($request->get('per_page', 15));

        return response()->json($inquiries);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'vehicle_id' => 'required|exists:vehicles,id',
            'customer_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'message' => 'nullable|string',
            'inquiry_type' => 'required|in:general,test_drive,financing,trade_in',
        ]);

        // Find or create customer
        $customer = Customer::firstOrCreate(
            ['email' => $validated['email']],
            [
                'name' => $validated['customer_name'],
                'phone' => $validated['phone'] ?? null,
            ]
        );

        $inquiry = CarInquiry::create([
            'vehicle_id' => $validated['vehicle_id'],
            'customer_id' => $customer->id,
            'customer_name' => $validated['customer_name'],
            'email' => $validated['email'],
            'phone' => $validated['phone'],
            'message' => $validated['message'],
            'inquiry_type' => $validated['inquiry_type'],
            'status' => 'pending',
        ]);

        $inquiry->load(['vehicle', 'customer']);

        return response()->json([
            'message' => 'Car inquiry submitted successfully',
            'data' => $inquiry
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(CarInquiry $carInquiry)
    {
        $carInquiry->load(['vehicle', 'customer']);

        return response()->json([
            'data' => $carInquiry
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, CarInquiry $carInquiry)
    {
        $validated = $request->validate([
            'customer_name' => 'sometimes|string|max:255',
            'email' => 'sometimes|email|max:255',
            'phone' => 'nullable|string|max:20',
            'message' => 'nullable|string',
            'inquiry_type' => 'sometimes|in:general,test_drive,financing,trade_in',
            'status' => 'sometimes|in:pending,contacted,scheduled,sold,cancelled',
            'contacted_at' => 'nullable|date',
            'scheduled_date' => 'nullable|date',
            'notes' => 'nullable|string',
        ]);

        // Update contacted_at if status changed to contacted
        if (isset($validated['status']) && $validated['status'] === 'contacted' && $carInquiry->status !== 'contacted') {
            $validated['contacted_at'] = now();
        }

        $carInquiry->update($validated);
        $carInquiry->load(['vehicle', 'customer']);

        return response()->json([
            'message' => 'Car inquiry updated successfully',
            'data' => $carInquiry
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CarInquiry $carInquiry)
    {
        $carInquiry->delete();

        return response()->json([
            'message' => 'Car inquiry deleted successfully'
        ]);
    }
}
